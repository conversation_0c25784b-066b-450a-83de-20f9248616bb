# Performance Monitoring Quick Start Guide

## 🚀 How to Monitor Your App Performance

### **1. Web Dashboard (Easiest)**

Visit the performance monitoring page in your admin panel:
```
https://your-domain.com/admin/performance
```

**What you'll see:**
- 📊 **Overview Cards**: Total operations, average duration, fastest/slowest operations
- ⚠️ **Slow Operations Table**: Any operation taking >1 second
- 🔄 **Auto-refresh**: Toggle to refresh every 10 seconds
- 🎯 **Filters**: Filter by operation type (DB, API, wave-history, etc.)

### **2. API Endpoints (For Scripts/Automation)**

```bash
# Get all performance stats
curl https://your-domain.com/api/admin/performance

# Filter by database operations only
curl https://your-domain.com/api/admin/performance?operation=DB

# Filter by specific endpoint
curl https://your-domain.com/api/admin/performance?operation=wave-history

# Clear all metrics (reset counters)
curl -X DELETE https://your-domain.com/api/admin/performance
```

### **3. Command Line Script**

Use the included monitoring script:

```bash
# Basic monitoring
node scripts/monitor-performance.js --url https://your-domain.com

# Watch mode (refreshes every 10 seconds) - RECOMMENDED
node scripts/monitor-performance.js --url https://your-domain.com --watch

# Filter by specific operations
node scripts/monitor-performance.js --operation "DB" --watch
node scripts/monitor-performance.js --operation "wave-history" --watch

# Clear metrics
node scripts/monitor-performance.js --clear --url https://your-domain.com

# Get raw JSON output
node scripts/monitor-performance.js --json --url https://your-domain.com
```

## 📈 What to Look For

### **Good Performance Indicators:**
- ✅ Average duration < 500ms
- ✅ No operations in "Slow Operations" table
- ✅ Consistent response times
- ✅ Low memory usage

### **Warning Signs:**
- ⚠️ Average duration > 1 second
- ⚠️ Multiple entries in "Slow Operations" table
- ⚠️ Increasing response times over time
- ⚠️ Operations consistently taking >2 seconds

### **Critical Issues:**
- 🚨 Average duration > 3 seconds
- 🚨 Many operations timing out
- 🚨 Memory usage continuously increasing
- 🚨 Server becoming unresponsive

## 🔧 Quick Actions

### **If You See Slow Performance:**

1. **Check the Performance Dashboard**
   - Look for patterns in slow operations
   - Note which endpoints are slowest

2. **Clear Cache if Needed**
   ```bash
   curl -X DELETE https://your-domain.com/api/admin/performance
   ```

3. **Check Server Resources**
   - CPU usage
   - Memory usage
   - Database performance

4. **Monitor for 10-15 minutes**
   - See if performance improves
   - Look for recurring slow operations

### **Daily Monitoring Routine:**

1. **Morning Check** (2 minutes):
   - Visit `/admin/performance`
   - Check average duration
   - Look for any slow operations

2. **After Deployments** (5 minutes):
   - Monitor for 30 minutes after deployment
   - Compare performance before/after
   - Clear metrics before deployment for clean comparison

3. **Weekly Review** (10 minutes):
   - Review performance trends
   - Clear old metrics
   - Check for memory leaks

## 📊 Understanding the Data

### **Response Format:**
```json
{
  "success": true,
  "stats": {
    "count": 45,              // Total operations tracked
    "avgDuration": 234,       // Average time in milliseconds
    "minDuration": 12,        // Fastest operation
    "maxDuration": 1850,      // Slowest operation
    "slowOperations": [...]   // Operations >1 second
  },
  "timestamp": "2024-12-21T10:30:45.123Z"
}
```

### **Operation Types:**
- `DB: operation-name` - Database queries
- `API: operation-name` - API endpoint calls
- `wave-history` - Wave history queries
- `detailed-analytics` - Analytics aggregations
- `comments` - Comment fetching

## 🎯 Performance Targets

| Metric | Target | Warning | Critical |
|--------|--------|---------|----------|
| Average Duration | <500ms | 500ms-1s | >1s |
| Individual Operations | <1s | 1s-2s | >2s |
| Cache Hit Rate | >70% | 50-70% | <50% |
| Slow Operations | 0 | 1-3 | >3 |

## 🚨 Emergency Response

If your app becomes very slow:

1. **Immediate Actions:**
   ```bash
   # Clear performance cache
   curl -X DELETE https://your-domain.com/api/admin/performance
   
   # Check current performance
   node scripts/monitor-performance.js --url https://your-domain.com --json
   ```

2. **Check Server Logs:**
   - Look for "Slow operation detected" warnings
   - Check for database connection issues
   - Monitor memory usage

3. **Contact Support:**
   - Share performance monitoring output
   - Include server resource usage
   - Note when the issue started

Remember: The monitoring system tracks operations in real-time, so you'll see immediate feedback on any performance improvements or issues!

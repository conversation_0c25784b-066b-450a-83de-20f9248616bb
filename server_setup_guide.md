# Server Setup Guide - Staging & Production

## Color Scheme
- **Staging**: <PERSON><PERSON> (cyan) - `\033[01;36m`
- **Production**: Red - `\033[01;31m` (for safety awareness)

## Staging Server Setup

### Option 1: Automated Script
1. Upload `rename_staging_server.sh` to your staging server
2. Make executable: `chmod +x rename_staging_server.sh`
3. Run: `./rename_staging_server.sh`
4. Apply changes: `source ~/.bashrc`

### Option 2: Manual Setup
```bash
# Rename the server
sudo hostnamectl set-hostname staging.echo360.app

# Update hosts file
echo "********* staging.echo360.app staging" | sudo tee -a /etc/hosts

# Change prompt color to teal
sed -i 's/\\\[\\033\[01;32m\\\]/\\[\\033[01;36m\\]/g' ~/.bashrc

# Apply changes
source ~/.bashrc
```

## Production Server Setup

### Option 1: Automated Script
1. Upload `setup_production_server.sh` to your production server
2. Make executable: `chmod +x setup_production_server.sh`
3. Run: `./setup_production_server.sh`
4. Apply changes: `source ~/.bashrc`

### Option 2: Manual Setup
```bash
# Rename the server
sudo hostnamectl set-hostname production.echo360.app

# Update hosts file
echo "********* production.echo360.app production" | sudo tee -a /etc/hosts

# Change prompt color to red (for safety)
sed -i 's/\\\[\\033\[01;32m\\\]/\\[\\033[01;31m\\]/g' ~/.bashrc

# Apply changes
source ~/.bashrc
```

## What You'll See

### Staging Server
```
<EMAIL>:~/echo360 (main)$
```
- Hostname: `staging.echo360.app`
- Color: Teal (cyan)
- Safe for testing and development

### Production Server
```
<EMAIL>:~/echo360 (main)$
```
- Hostname: `production.echo360.app`
- Color: Red (for safety awareness)
- Be extra careful here!

## SSH Connection Updates

After renaming, you'll need to update your SSH connections:

### Update SSH config (~/.ssh/config)
```bash
# Staging
Host staging
    HostName your-staging-ip
    User dev-ops
    Port 22

# Production
Host production
    HostName your-production-ip
    User dev-ops
    Port 22
```

### Then connect using:
```bash
ssh staging    # Instead of ssh dev-ops@staging-ip
ssh production # Instead of ssh dev-ops@production-ip
```

## Verification

To verify the setup worked:
```bash
# Check hostname
hostname

# Check prompt color and format
echo $PS1

# Should show the new hostname and color
```

## Troubleshooting

If colors don't work:
```bash
# Check terminal type
echo $TERM

# Set if needed
export TERM=xterm-256color
```

## Reverting Changes

If you need to revert:
```bash
# Restore original hostname
sudo hostnamectl set-hostname original-hostname

# Restore original .bashrc
cp ~/.bashrc.backup.* ~/.bashrc
source ~/.bashrc
```

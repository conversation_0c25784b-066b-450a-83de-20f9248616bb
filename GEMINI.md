# Project Overview

This is a Next.js application for a survey platform called Echo360. It uses Material-UI for the frontend, and MongoDB as the database. The application is written in TypeScript.

The main purpose of this application is to provide a platform for creating and administering 360° feedback surveys. It is designed to be white-labeled and scalable for use by HR departments, coaches, and consultants.

# Development Conventions

*   **UI Components:** The project uses Material-UI (MUI) as its component library. New components should be built using MUI components as base building blocks and follow Material Design guidelines.
*   **Styling:** The `sx` prop and `styled` API from MUI should be used for custom styling.
*   **Code Formatting:** The project uses Prettier for code formatting. It is recommended to have the Prettier plugin installed in your IDE.
*   **Linting:** The project uses ESLint for linting. Before committing any changes, make sure the code is free of linting errors by running `npm run lint`.
*   **Committing:** Before committing any changes, make sure the project builds properly by running `npm run build`.

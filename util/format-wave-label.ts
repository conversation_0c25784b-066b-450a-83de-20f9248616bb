/**
 * Formats a wave label with date prefix for dropdown display.
 * Converts "2024 NAZ early-year eval" to "Jan '24 - 2024 NAZ early-year eval"
 * 
 * @param waveName - The original wave name
 * @param createdAt - The wave creation date (Date object or ISO string)
 * @returns Formatted label with date prefix
 */
export function formatWaveLabel(waveName: string, createdAt: Date | string): string {
  const date = typeof createdAt === 'string' ? new Date(createdAt) : createdAt;

  // Format as "Jan '24" - short month and 2-digit year with apostrophe
  const month = date.toLocaleDateString('en-US', { month: 'short' });
  const year = date.toLocaleDateString('en-US', { year: '2-digit' });
  const monthYear = `${month} '${year}`;

  return `${monthYear} - ${waveName}`;
}

/**
 * Creates a wave dropdown option with both display label and value.
 * 
 * @param wave - Wave object with name and createdAt
 * @returns Object with label for display and value for filtering
 */
export interface WaveDropdownOption {
  label: string;  // Display label with date prefix
  value: string;  // Original wave name for filtering
}

export function createWaveDropdownOption(wave: { name: string; createdAt: Date | string }): WaveDropdownOption {
  return {
    label: formatWaveLabel(wave.name, wave.createdAt),
    value: wave.name,
  };
}

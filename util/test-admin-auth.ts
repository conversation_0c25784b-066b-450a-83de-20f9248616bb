import 'dotenv-flow/config';
import dbConnect from '../lib/mongodb';
import { AdminModel } from '../lib/models/admin';

async function testAdminAuth() {
  try {
    await dbConnect();

    console.log('🔍 Testing Admin Authentication...\n');

    // List all admins
    const admins = await AdminModel.find({}).select('email name role isActive createdAt');
    console.log('📋 Found admins:');
    console.log('='.repeat(50));
    
    admins.forEach((admin, index) => {
      console.log(`${index + 1}. Email: ${admin.email}`);
      console.log(`   Name: ${admin.name}`);
      console.log(`   Role: ${admin.role}`);
      console.log(`   Active: ${admin.isActive}`);
      console.log(`   Created: ${admin.createdAt}`);
      console.log('-'.repeat(30));
    });

    // Test password comparison for superadmin
    const superadmin = await AdminModel.findOne({ 
      email: '<EMAIL>' 
    });

    if (superadmin) {
      console.log('\n🔐 Testing password verification for superadmin...');
      
      // Test correct password
      const correctPassword = await superadmin.comparePassword('SuperAdmin123!');
      console.log(`✅ Correct password test: ${correctPassword ? 'PASS' : 'FAIL'}`);
      
      // Test incorrect password
      const incorrectPassword = await superadmin.comparePassword('wrongpassword');
      console.log(`❌ Incorrect password test: ${incorrectPassword ? 'FAIL' : 'PASS'}`);
      
      console.log('\n📊 Superadmin Details:');
      console.log(`Email: ${superadmin.email}`);
      console.log(`Name: ${superadmin.name}`);
      console.log(`Role: ${superadmin.role}`);
      console.log(`Active: ${superadmin.isActive}`);
      console.log(`Last Login: ${superadmin.lastLoginAt || 'Never'}`);
    } else {
      console.log('❌ Superadmin not found!');
    }

    // Test regular admin too
    const regularAdmin = await AdminModel.findOne({ 
      email: '<EMAIL>' 
    });

    if (regularAdmin) {
      console.log('\n🔐 Testing password verification for regular admin...');
      
      // Test correct password
      const correctPassword = await regularAdmin.comparePassword('RegularAdmin123!');
      console.log(`✅ Correct password test: ${correctPassword ? 'PASS' : 'FAIL'}`);
      
      console.log('\n📊 Regular Admin Details:');
      console.log(`Email: ${regularAdmin.email}`);
      console.log(`Name: ${regularAdmin.name}`);
      console.log(`Role: ${regularAdmin.role}`);
      console.log(`Active: ${regularAdmin.isActive}`);
      console.log(`Last Login: ${regularAdmin.lastLoginAt || 'Never'}`);
    } else {
      console.log('❌ Regular admin not found!');
    }

    console.log('\n✅ Authentication test completed!');
    
    // Check JWT_SECRET
    console.log('\n🔑 Environment Check:');
    console.log(`JWT_SECRET exists: ${process.env.JWT_SECRET ? 'YES' : 'NO'}`);
    console.log(`MONGODB_URI exists: ${process.env.MONGODB_URI ? 'YES' : 'NO'}`);
    
    if (!process.env.JWT_SECRET) {
      console.log('⚠️  WARNING: JWT_SECRET is not set! This will cause authentication to fail.');
    }

  } catch (error) {
    console.error('❌ Error testing admin auth:', error);
  } finally {
    process.exit(0);
  }
}

testAdminAuth();

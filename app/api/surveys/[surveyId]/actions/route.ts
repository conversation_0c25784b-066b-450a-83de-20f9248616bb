import { NextRequest, NextResponse } from 'next/server';
import { Types } from 'mongoose';
import dbConnect from '../../../../../lib/mongodb';
import { SurveyModel, ISurveyDoc } from '../../../../../lib/models/survey';
import { UserPasscodeModel } from '../../../../../lib/models/user-passcode';
import sendSimpleMessages from '../../../../../lib/mailgun';
import generatePasscode from '../../../../../util/generate-passcode';
import {
  ISurveyActionRequest,
  ISurveyActionResponse,
} from '../../../../../lib/types/survey-details';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ surveyId: string }> }
) {
  try {
    await dbConnect();

    const { surveyId } = await params;
    const body: Omit<ISurveyActionRequest, 'surveyId'> = await request.json();
    const { action, data } = body;

    if (!Types.ObjectId.isValid(surveyId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid survey ID' },
        { status: 400 }
      );
    }

    const survey = await SurveyModel.findById(surveyId);
    if (!survey) {
      return NextResponse.json(
        { success: false, error: 'Survey not found' },
        { status: 404 }
      );
    }

    let response: ISurveyActionResponse;

    switch (action) {
      case 'remove':
        response = await handleRemoveSurvey(surveyId, data?.reason);
        break;

      case 'restore':
        response = await handleRestoreSurvey(surveyId);
        break;

      case 'resend_invite':
        response = await handleResendInvite(survey);
        break;

      case 'send_reminder':
        response = await handleSendReminder(survey);
        break;

      case 'save':
        response = await handleEditSurvey(surveyId, data || {});
        break;
      
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error handling survey action:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process survey action' 
      },
      { status: 500 }
    );
  }
}

async function handleRemoveSurvey(
  surveyId: string,
  reason?: string
): Promise<ISurveyActionResponse> {
  try {
    // For now, we'll add a 'removed' field to track soft deletion
    // In the future, this could be moved to a separate collection
    const updatedSurvey = await SurveyModel.findByIdAndUpdate(
      surveyId,
      {
        $set: {
          isRemoved: true,
          removedAt: new Date(),
          removalReason: reason || 'Removed by admin'
        }
      },
      { new: true }
    );

    if (!updatedSurvey) {
      return {
        success: false,
        error: 'Survey not found'
      };
    }

    return {
      success: true,
      message: 'Survey removed successfully. This action can be undone if needed.'
    };
  } catch (error) {
    console.error('Error removing survey:', error);
    return {
      success: false,
      error: 'Failed to remove survey'
    };
  }
}

async function handleRestoreSurvey(surveyId: string): Promise<ISurveyActionResponse> {
  try {
    const updatedSurvey = await SurveyModel.findByIdAndUpdate(
      surveyId,
      {
        $unset: {
          isRemoved: 1,
          removedAt: 1,
          removalReason: 1
        }
      },
      { new: true }
    );

    if (!updatedSurvey) {
      return {
        success: false,
        error: 'Survey not found'
      };
    }

    return {
      success: true,
      message: 'Survey restored successfully.'
    };
  } catch (error) {
    console.error('Error restoring survey:', error);
    return {
      success: false,
      error: 'Failed to restore survey'
    };
  }
}

async function handleResendInvite(survey: ISurveyDoc): Promise<ISurveyActionResponse> {
  try {
    // Check if survey has a valid email
    if (!survey.userEmail || !survey.userEmail.trim()) {
      return {
        success: false,
        error: 'Survey has no email address'
      };
    }

    // Send email using the existing email system
    await sendSimpleMessages([survey]);

    return {
      success: true,
      message: `Invitation resent to ${survey.userEmail}`
    };
  } catch (error) {
    console.error('Error resending invite:', error);
    return {
      success: false,
      error: 'Failed to resend invitation'
    };
  }
}

async function handleSendReminder(survey: ISurveyDoc): Promise<ISurveyActionResponse> {
  try {
    // Check if survey has a valid email
    if (!survey.userEmail || !survey.userEmail.trim()) {
      return {
        success: false,
        error: 'Survey has no email address'
      };
    }

    // For now, use the same email system as invites
    // In the future, this could use a different email template
    await sendSimpleMessages([survey]);

    return {
      success: true,
      message: `Reminder sent to ${survey.userEmail}`
    };
  } catch (error) {
    console.error('Error sending reminder:', error);
    return {
      success: false,
      error: 'Failed to send reminder'
    };
  }
}

async function handleEditSurvey(
  surveyId: string,
  data: Record<string, unknown>
): Promise<ISurveyActionResponse> {
  try {
    // Get the original survey to check for email changes
    const originalSurvey = await SurveyModel.findById(surveyId);
    if (!originalSurvey) {
      return {
        success: false,
        error: 'Survey not found'
      };
    }

    const updateData: Record<string, unknown> = {};

    // Only update provided fields
    if (data.userName !== undefined) updateData.userName = data.userName;
    if (data.userEmail !== undefined) updateData.userEmail = data.userEmail;
    if (data.agencyName !== undefined) updateData.agencyName = data.agencyName;
    if (data.brand !== undefined) updateData.brand = data.brand;
    if (data.country !== undefined) updateData.country = data.country;
    if (data.region !== undefined) updateData.region = data.region;
    if (data.notes !== undefined) updateData.notes = data.notes;

    const updatedSurvey = await SurveyModel.findByIdAndUpdate(
      surveyId,
      { $set: updateData },
      { new: true }
    );

    if (!updatedSurvey) {
      return {
        success: false,
        error: 'Survey not found'
      };
    }

    let newPasscode: string | null = null;
    let passcodeMessage = '';

    // If email was changed, handle passcode logic
    if (data.userEmail && data.userEmail !== originalSurvey.userEmail) {
      try {
        const newEmail = data.userEmail as string;
        const waveId = originalSurvey.waveId;

        // Step 1: Check if new email already has a passcode for this wave
        const existingNewEmailPasscode = await UserPasscodeModel.findOne({
          userEmail: newEmail,
          waveId: waveId
        });

        if (existingNewEmailPasscode) {
          // Case 1: New email already has a passcode - just use it
          newPasscode = existingNewEmailPasscode.passcode;
          passcodeMessage = ` Email updated. Using existing passcode (${newPasscode}) for this email address.`;

          // Don't delete the old email's passcode - leave it intact for other surveys
        } else {
          // Case 2: New email doesn't have a passcode - generate a new one
          let passcode;
          let attempts = 0;

          do {
            passcode = generatePasscode();
            attempts++;
          } while (
            await UserPasscodeModel.findOne({ passcode }) &&
            attempts < 10
          );

          if (attempts >= 10) {
            throw new Error('Could not generate unique passcode');
          }

          // Create new passcode entry for the new email
          await UserPasscodeModel.create({
            userEmail: newEmail,
            passcode: passcode!,
            waveId: waveId,
            expiresAt: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days
            isActive: true,
          });

          newPasscode = passcode!;
          passcodeMessage = ` Email updated. New passcode (${newPasscode}) generated for the new email address.`;
        }
      } catch (error) {
        console.error('Error handling passcode for email change:', error);
        passcodeMessage = ' Email updated, but there was an issue with the passcode. Please refresh to see the current state.';
      }
    }

    return {
      success: true,
      message: `Survey updated successfully.${passcodeMessage}`
    };
  } catch (error) {
    console.error('Error editing survey:', error);
    return {
      success: false,
      error: 'Failed to update survey'
    };
  }
}

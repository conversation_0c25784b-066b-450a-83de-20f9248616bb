import { NextResponse } from 'next/server';
import { getUniqueBrands } from '../../../../lib/api/surveys';
import { monitorApiOperation } from '../../../../lib/performance';

export async function GET() {
  try {
    const brands = await monitorApiOperation(
      'get-unique-brands',
      () => getUniqueBrands()
    );
    return NextResponse.json(brands);
  } catch (error) {
    console.error('Error fetching unique brands:', error);
    return NextResponse.json(
      { error: 'Failed to fetch brands' },
      { status: 500 }
    );
  }
}
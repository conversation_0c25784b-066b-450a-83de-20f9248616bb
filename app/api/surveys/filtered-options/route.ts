import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '../../../../lib/mongodb';
import { SurveyModel } from '../../../../lib/models/survey';
import { monitorDbOperation } from '../../../../lib/performance';

export async function GET(request: NextRequest) {
  try {
    await dbConnect();
    
    const { searchParams } = new URL(request.url);
    
    // Get current filter selections
    const selectedAgency = searchParams.get('agency');
    const selectedBrand = searchParams.get('brand');
    const selectedRegion = searchParams.get('region');
    const selectedWave = searchParams.get('wave');
    
    // Build base aggregation pipeline to exclude seed waves
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const basePipeline: any[] = [
      {
        $lookup: {
          from: 'waves',
          localField: 'waveId',
          foreignField: '_id',
          as: 'wave'
        }
      },
      { $unwind: '$wave' },
      {
        $match: {
          'wave.status': { $ne: 'seed' },
          'isRemoved': { $ne: true }
        }
      }
    ];
    
    // Add wave filter if specified
    if (selectedWave) {
      const waveNames = selectedWave.split(',').map(name => name.trim());
      basePipeline.push({
        $match: {
          'wave.name': { $in: waveNames }
        }
      });
    }
    
    // Get available agencies (filter by brand and region, but NOT by agency)
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const agencyMatchConditions: any = {};
    if (selectedBrand && selectedBrand !== 'All Brands') {
      agencyMatchConditions.brand = selectedBrand;
    }
    if (selectedRegion && selectedRegion !== 'All Regions') {
      agencyMatchConditions.country = selectedRegion;
    }
    
    const agenciesPipeline = [...basePipeline];
    if (Object.keys(agencyMatchConditions).length > 0) {
      agenciesPipeline.push({ $match: agencyMatchConditions });
    }
    agenciesPipeline.push(
      { $group: { _id: '$agencyName' } },
      { $sort: { _id: 1 as const } }
    );
    
    // Get available brands (filter by agency and region, but NOT by brand)
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const brandMatchConditions: any = {};
    if (selectedAgency && selectedAgency !== 'All Agencies') {
      brandMatchConditions.agencyName = selectedAgency;
    }
    if (selectedRegion && selectedRegion !== 'All Regions') {
      brandMatchConditions.country = selectedRegion;
    }
    
    const brandsPipeline = [...basePipeline];
    if (Object.keys(brandMatchConditions).length > 0) {
      brandsPipeline.push({ $match: brandMatchConditions });
    }
    brandsPipeline.push(
      { $group: { _id: '$brand' } },
      { $sort: { _id: 1 as const } }
    );
    
    // Get available regions (filter by agency and brand, but NOT by region)
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const regionMatchConditions: any = {};
    if (selectedAgency && selectedAgency !== 'All Agencies') {
      regionMatchConditions.agencyName = selectedAgency;
    }
    if (selectedBrand && selectedBrand !== 'All Brands') {
      regionMatchConditions.brand = selectedBrand;
    }
    
    const regionsPipeline = [...basePipeline];
    if (Object.keys(regionMatchConditions).length > 0) {
      regionsPipeline.push({ $match: regionMatchConditions });
    }
    regionsPipeline.push(
      { $group: { _id: '$country' } },
      { $sort: { _id: 1 as const } }
    );
    
    // Get available agency types (filter by agency, brand, and region, but NOT by agency type)
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const agencyTypeMatchConditions: any = {};
    if (selectedAgency && selectedAgency !== 'All Agencies') {
      agencyTypeMatchConditions.agencyName = selectedAgency;
    }
    if (selectedBrand && selectedBrand !== 'All Brands') {
      agencyTypeMatchConditions.brand = selectedBrand;
    }
    if (selectedRegion && selectedRegion !== 'All Regions') {
      agencyTypeMatchConditions.country = selectedRegion;
    }

    const agencyTypesPipeline = [...basePipeline];
    if (Object.keys(agencyTypeMatchConditions).length > 0) {
      agencyTypesPipeline.push({ $match: agencyTypeMatchConditions });
    }
    agencyTypesPipeline.push(
      { $group: { _id: '$agencyType' } },
      { $sort: { _id: 1 as const } }
    );

    const [agenciesResult, brandsResult, regionsResult, agencyTypesResult] = await monitorDbOperation(
      'filtered-options-aggregation',
      () => Promise.all([
        SurveyModel.aggregate(agenciesPipeline),
        SurveyModel.aggregate(brandsPipeline),
        SurveyModel.aggregate(regionsPipeline),
        SurveyModel.aggregate(agencyTypesPipeline)
      ]),
      {
        selectedAgency: !!selectedAgency,
        selectedBrand: !!selectedBrand,
        selectedRegion: !!selectedRegion,
        selectedWave: !!selectedWave
      }
    );

    const agencies = ['All Agencies', ...agenciesResult.map(item => item._id)];
    const brands = ['All Brands', ...brandsResult.map(item => item._id)];
    const regions = ['All Regions', ...regionsResult.map(item => item._id)];
    const agencyTypes = ['All Agency Types', ...agencyTypesResult.map(item => item._id)];

    return NextResponse.json({
      agencies,
      brands,
      regions,
      agencyTypes
    });
  } catch (error) {
    console.error('Error fetching filtered options:', error);
    return NextResponse.json(
      { error: 'Failed to fetch filtered options' },
      { status: 500 }
    );
  }
} 
import { NextResponse } from 'next/server';
import { getUniqueAgencyTypes } from '../../../../lib/api/surveys';

export async function GET() {
  try {
    const agencyTypes = await getUniqueAgencyTypes();
    return NextResponse.json(agencyTypes);
  } catch (error) {
    console.error('Error fetching unique agency types:', error);
    return NextResponse.json(
      { error: 'Failed to fetch agency types' },
      { status: 500 }
    );
  }
}

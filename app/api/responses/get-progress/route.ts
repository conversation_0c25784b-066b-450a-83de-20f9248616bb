import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '../../../../lib/mongodb';
import { ResponseModel } from '../../../../lib/models/response';
import { SurveyModel } from '../../../../lib/models/survey';

export async function GET(request: NextRequest) {
  try {
    await dbConnect();
    
    const { searchParams } = new URL(request.url);
    const surveyId = searchParams.get('surveyId');
    const userEmail = searchParams.get('userEmail');

    if (!surveyId || !userEmail) {
      return NextResponse.json({ 
        error: 'Missing surveyId or userEmail parameter' 
      }, { status: 400 });
    }

    // Get survey details to determine question count
    const survey = await SurveyModel.findById(surveyId);
    if (!survey) {
      return NextResponse.json({
        error: 'Survey not found'
      }, { status: 404 });
    }

    const isABIOnAgency = survey.assessmentType === 'Anheuser Busch In-Bev-on-Agency';
    const totalQuestions = isABIOnAgency ? 6 : 5;

    // Find existing response for this survey and user
    const existingResponse = await ResponseModel.findOne({
      surveyId,
      userEmail,
    });

    if (!existingResponse) {
      return NextResponse.json({
        answers: {},
        isComplete: false,
        completedQuestions: 0,
        totalQuestions
      });
    }

    // Extract question answers from the response
    const answers: { [key: string]: string | number } = {};
    let completedQuestions = 0;

    // Handle questions 1-5 (all surveys)
    for (let i = 1; i <= 5; i++) {
      const scoreField = `q${i}Score`;
      const commentField = `q${i}Comment`;

      // Check if question is answered (including N/A responses stored as null)
      if (existingResponse[scoreField] !== undefined && existingResponse[commentField] !== undefined) {
        // Handle N/A responses (stored as null)
        if (existingResponse[scoreField] === null) {
          answers[`q${i}`] = 'N/A';
          answers[`q${i}-comment`] = 'N/A';
        } else {
          answers[`q${i}`] = existingResponse[scoreField].toString();
          answers[`q${i}-comment`] = existingResponse[commentField] || '';
        }
        completedQuestions++;
      }
    }

    // Handle question 6 (NPS) for ABI surveys - only needs score, no comment
    if (isABIOnAgency && existingResponse.q6Score !== undefined) {
      // NPS questions cannot be N/A, so no need to check for null
      answers[`q6`] = existingResponse.q6Score.toString();
      completedQuestions++;
    }

    const isComplete = completedQuestions === totalQuestions;

    return NextResponse.json({ 
      answers,
      isComplete,
      completedQuestions,
      totalQuestions
    });

  } catch (error) {
    console.error('Error fetching progress:', error);
    return NextResponse.json({ 
      error: 'Failed to fetch survey progress' 
    }, { status: 500 });
  }
} 
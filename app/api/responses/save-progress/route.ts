import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '../../../../lib/mongodb';
import { ResponseModel } from '../../../../lib/models/response';

export async function POST(request: NextRequest) {
  try {
    await dbConnect();
    const {
      surveyId,
      waveId,
      questionId,
      value,
      comment,
      userEmail,
      userName,
    } = await request.json();

    if (
      !surveyId ||
      !waveId ||
      !questionId ||
      value === undefined ||
      value === null ||
      !userEmail ||
      !userName
    ) {
      return NextResponse.json(
        {
          error:
            'Missing required information. Please refresh the page and try again. If the problem persists, contact <NAME_EMAIL> with error code: MISSING_FIELDS',
        },
        { status: 400 }
      );
    }

    // Convert questionId (e.g., "q1") to the score field name (e.g., "q1Score")
    const scoreField = `${questionId}Score`;
    const commentField = `${questionId}Comment`;

    // Check if an answer for this question already exists for this user/survey
    const existing = await ResponseModel.findOne({
      surveyId,
      userEmail,
    });

    // If response exists and this specific question is already answered
    if (existing && existing[scoreField] !== undefined) {
      // Check if the new answer is the same as existing
      const existingScore = existing[scoreField];
      const existingComment = existing[commentField] || '';

      // Handle N/A responses
      const newScore = value === 'N/A' ? null : parseInt(value, 10);
      const newComment = comment === 'N/A' ? null : (comment || '');

      // For NPS questions (q6), only compare scores since comments are optional
      const isNPSQuestion = questionId === 'q6';
      const answersMatch = isNPSQuestion
        ? existingScore === newScore
        : existingScore === newScore && existingComment === newComment;

      if (answersMatch) {
        // Same answer, return success without updating
        return NextResponse.json({
          success: true,
          message: 'Answer already saved',
        });
      } else {
        // Different answer, this shouldn't happen in normal flow but we'll allow it
        // This could happen if user somehow navigates back to a locked question
        return NextResponse.json(
          {
            error:
              'This question has already been answered. If you need to change your answer, please contact <NAME_EMAIL> with error code: ANSWER_LOCKED',
          },
          { status: 409 }
        );
      }
    }

    // Upsert the response document for this survey/user
    const updateFields: Record<string, unknown> = {
      surveyId,
      waveId,
      userEmail,
      userName,
    };

    // Handle N/A responses by storing as null
    if (value === 'N/A') {
      updateFields[scoreField] = null;
    } else {
      updateFields[scoreField] = parseInt(value, 10); // Ensure it's a number
    }

    // Handle comment field
    if (comment !== undefined && comment !== null) {
      if (comment === 'N/A') {
        updateFields[commentField] = null;
      } else {
        updateFields[commentField] = comment;
      }
    }

    const update = {
      $set: updateFields,
    };
    await ResponseModel.updateOne(
      { surveyId, userEmail }, // Remove waveId from query since it might not exist in existing docs
      update,
      { upsert: true }
    );

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error saving progress:', error);

    // More detailed error response for users
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error occurred';

    return NextResponse.json(
      {
        error:
          'We encountered a technical issue while saving your response. Please try again in a moment. If the problem continues, please contact our support <NAME_EMAIL> and include this error code: SAVE_ERROR',
        details: `Technical details: ${errorMessage}`,
        timestamp: new Date().toISOString(),
        errorCode: 'SAVE_ERROR',
      },
      { status: 500 }
    );
  }
}

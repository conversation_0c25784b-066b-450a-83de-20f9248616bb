import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '../../../../lib/mongodb';
import { ResponseModel } from '../../../../lib/models/response';

export async function GET(request: NextRequest) {
  try {
    await dbConnect();

    const { searchParams } = new URL(request.url);
    const focusType = searchParams.get('focusType') as 'agency' | 'brand' | 'region' | 'wave';
    const focusValue = searchParams.get('focusValue');
    const wave = searchParams.get('wave'); // Optional wave filter

    if (!focusType || !focusValue || focusValue === 'None') {
      return NextResponse.json({
        comments: ['No filter selected. Please select an Agency, Brand, or Region to view comments.']
      });
    }

    // Build aggregation pipeline
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const pipeline: any[] = [
      // Join with surveys to get agency/brand/region info
      {
        $lookup: {
          from: 'surveys',
          localField: 'surveyId',
          foreignField: '_id',
          as: 'survey'
        }
      },
      { $unwind: '$survey' },
      // Join with waves to get wave info and filter out seed data
      {
        $lookup: {
          from: 'waves',
          localField: 'survey.waveId',
          foreignField: '_id',
          as: 'wave'
        }
      },
      { $unwind: '$wave' },
      // Filter out seed waves
      {
        $match: {
          'wave.status': { $ne: 'seed' }
        }
      }
    ];

    // Add focus filter
    const focusFilterCondition: Record<string, unknown> = {};
    if (focusType === 'agency') {
      focusFilterCondition['survey.agencyName'] = focusValue;
    } else if (focusType === 'brand') {
      focusFilterCondition['survey.brand'] = focusValue;
    } else if (focusType === 'region') {
      focusFilterCondition['survey.country'] = focusValue;
    } else if (focusType === 'wave') {
      focusFilterCondition['wave.name'] = focusValue;
    }

    // Add wave filter if provided
    if (wave && focusType !== 'wave') {
      const waves = wave.split(',').map(w => w.trim());
      if (waves.length === 1) {
        focusFilterCondition['wave.name'] = waves[0];
      } else {
        focusFilterCondition['wave.name'] = { $in: waves };
      }
    }

    pipeline.push({ $match: focusFilterCondition });

    // Project to get all comments
    pipeline.push({
      $project: {
        assessorName: 1,
        assessorType: 1,
        q1Comment: 1,
        q2Comment: 1,
        q3Comment: 1,
        q4Comment: 1,
        q5Comment: 1,
        'survey.agencyName': 1,
        'survey.brand': 1,
        'survey.country': 1,
        'survey.userName': 1, // Also get assessor name from survey
        'survey.assessmentType': 1, // Also get assessment type from survey
        'wave.name': 1,
        createdAt: 1
      }
    });

    // Limit to recent responses to avoid overwhelming the UI and improve performance
    pipeline.push({ $sort: { createdAt: -1 } });
    pipeline.push({ $limit: 25 }); // Reduced from 50 to 25 for better performance

    const responses = await ResponseModel.aggregate(pipeline);

    // Debug logging to understand the data structure
    if (responses.length > 0) {
      console.log('Sample response structure:', {
        allFields: Object.keys(responses[0]),
        assessorType: responses[0].assessorType,
        assessorName: responses[0].assessorName,
        surveyFields: Object.keys(responses[0].survey || {}),
        sampleResponse: responses[0],
      });
    }

    // Flatten all comments into a single array
    const comments: string[] = [];
    
    responses.forEach(response => {
      // Add all non-empty comments from all questions
      [response.q1Comment, response.q2Comment, response.q3Comment, response.q4Comment, response.q5Comment]
        .filter(comment => comment && comment.trim().length > 0)
        .forEach(comment => {
          // Add context about who made the comment
          // For AB InBev assessors, show assessment type only (anonymous)
          // For third-party agencies, format as "Agency: [AgencyName]" (anonymous)
          let contextPrefix;
          
          // Try to get assessor info from response first, then from survey
          const assessorType = response.assessorType || response.survey?.assessmentType || '';
          const agencyName = response.survey?.agencyName || 'Unknown Agency';
          
          if (assessorType.includes('Anheuser Busch')) {
            contextPrefix = `(${assessorType}): `;
          } else {
            contextPrefix = `(${agencyName}): `;
          }
          comments.push(contextPrefix + comment.trim());
        });
    });

    // If no comments found, return a helpful message
    if (comments.length === 0) {
      comments.push(`No comments available for ${focusValue} in the selected criteria.`);
    }

    return NextResponse.json({ comments });

  } catch (error) {
    console.error('Error fetching comments:', error);
    return NextResponse.json(
      { error: 'Failed to fetch comments' },
      { status: 500 }
    );
  }
} 
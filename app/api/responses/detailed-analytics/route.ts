import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '../../../../lib/mongodb';
import { ResponseModel } from '../../../../lib/models/response';
import { monitorDbOperation } from '../../../../lib/performance';

export async function GET(request: NextRequest) {
  try {
    await dbConnect();

    const { searchParams } = new URL(request.url);
    
    const filters: Record<string, string> = {};
    
    // Extract filter parameters
    const agencyName = searchParams.get('agencyName');
    const brand = searchParams.get('brand');
    const region = searchParams.get('region');
    const assessmentType = searchParams.get('assessmentType');
    const wave = searchParams.get('wave');
    
    if (agencyName) filters.agencyName = agencyName;
    if (brand) filters.brand = brand;
    if (region) filters.region = region;
    if (assessmentType) filters.assessmentType = assessmentType;
    if (wave) filters.wave = wave;

    // Build aggregation pipeline
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const pipeline: any[] = [
      // Join with surveys to get agency/brand info
      {
        $lookup: {
          from: 'surveys',
          localField: 'surveyId',
          foreignField: '_id',
          as: 'survey'
        }
      },
      { $unwind: '$survey' },
      // Join with waves to filter out seed data
      {
        $lookup: {
          from: 'waves',
          localField: 'survey.waveId',
          foreignField: '_id',
          as: 'wave'
        }
      },
      { $unwind: '$wave' },
      // Filter out seed waves and removed surveys
      {
        $match: {
          'wave.status': { $ne: 'seed' },
          'survey.isRemoved': { $ne: true }
        }
      }
    ];

    // Add filters if provided
    if (Object.keys(filters).length > 0) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const matchConditions: any = {};
      if (filters.agencyName) matchConditions['survey.agencyName'] = filters.agencyName;
      if (filters.brand) matchConditions['survey.brand'] = filters.brand;
      if (filters.region) matchConditions['survey.country'] = filters.region;
      if (filters.assessmentType) matchConditions['survey.assessmentType'] = filters.assessmentType;
      if (filters.wave) {
        // Handle multiple waves (comma-separated)
        const waves = filters.wave.split(',').map(w => w.trim());
        if (waves.length === 1) {
          matchConditions['wave.name'] = waves[0];
        } else {
          matchConditions['wave.name'] = { $in: waves };
        }
      }
      
      pipeline.push({ $match: matchConditions });
    }

    // Group and calculate averages by agency ONLY (not by brand/region/assessmentType)
    pipeline.push({
      $group: {
        _id: '$survey.agencyName',
        averageScore: {
          $avg: {
            $avg: ['$q1Score', '$q2Score', '$q3Score', '$q4Score', '$q5Score']
          }
        },
        responseCount: { $sum: 1 },
        avgNpsScore: { $avg: '$npsScore' },
        q1Avg: { $avg: '$q1Score' },
        q2Avg: { $avg: '$q2Score' },
        q3Avg: { $avg: '$q3Score' },
        q4Avg: { $avg: '$q4Score' },
        q5Avg: { $avg: '$q5Score' },
        // Get the first agency type for display (agencies typically have one type)
        agencyType: { $first: '$survey.agencyType' },
        // Count distinct brands and regions this agency works with
        brands: { $addToSet: '$survey.brand' },
        regions: { $addToSet: '$survey.country' },
      }
    });

    // Format output
    pipeline.push({
      $project: {
        _id: 0,
        agencyName: '$_id',
        agencyType: 1,
        averageScore: { $round: ['$averageScore', 2] },
        responseCount: 1,
        npsScore: { $round: ['$avgNpsScore', 2] },
        q1Avg: { $round: ['$q1Avg', 2] },
        q2Avg: { $round: ['$q2Avg', 2] },
        q3Avg: { $round: ['$q3Avg', 2] },
        q4Avg: { $round: ['$q4Avg', 2] },
        q5Avg: { $round: ['$q5Avg', 2] },
        brandCount: { $size: '$brands' },
        regionCount: { $size: '$regions' },
      }
    });

    // Sort by average score descending
    pipeline.push({ $sort: { averageScore: -1 } });

    const analytics = await monitorDbOperation(
      'detailed-analytics-aggregation',
      () => ResponseModel.aggregate(pipeline),
      { filtersCount: Object.keys(filters).length, pipelineStages: pipeline.length }
    );

    return NextResponse.json(analytics);
  } catch (error) {
    console.error('Error fetching detailed analytics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch detailed analytics' },
      { status: 500 }
    );
  }
} 
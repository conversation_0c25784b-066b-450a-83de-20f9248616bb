import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '../../../../lib/mongodb';
import { ResponseModel } from '../../../../lib/models/response';
import { SurveyModel } from '../../../../lib/models/survey';

export async function POST(request: NextRequest) {
  try {
    await dbConnect();

    const { surveyIds, userEmail } = await request.json();

    if (!surveyIds || !Array.isArray(surveyIds) || !userEmail) {
      return NextResponse.json({
        error: 'Missing surveyIds array or userEmail'
      }, { status: 400 });
    }

    // Find existing responses for these surveys and user
    const existingResponses = await ResponseModel.find({
      surveyId: { $in: surveyIds },
      userEmail,
    });

    // Get survey details to determine question count based on assessment type
    const surveys = await SurveyModel.find({
      _id: { $in: surveyIds }
    });

    // Create a map of surveyId to progress info
    const progressMap: { [surveyId: string]: {
      completedQuestions: number;
      totalQuestions: number;
      isComplete: boolean;
      percentComplete: number;
    } } = {};

    // Initialize all surveys with 0 progress
    surveyIds.forEach((surveyId: string) => {
      const survey = surveys.find(s => s._id.toString() === surveyId);
      const isABIOnAgency = survey?.assessmentType === 'Anheuser Busch In-Bev-on-Agency';
      const totalQuestions = isABIOnAgency ? 6 : 5; // ABI surveys have NPS question

      progressMap[surveyId] = {
        completedQuestions: 0,
        totalQuestions,
        isComplete: false,
        percentComplete: 0
      };
    });

    // Update progress for surveys that have responses
    existingResponses.forEach(response => {
      const surveyId = response.surveyId.toString();
      const survey = surveys.find(s => s._id.toString() === surveyId);
      const isABIOnAgency = survey?.assessmentType === 'Anheuser Busch In-Bev-on-Agency';
      const totalQuestions = isABIOnAgency ? 6 : 5;
      let completedQuestions = 0;

      // Count completed questions (1-5 for all surveys)
      for (let i = 1; i <= 5; i++) {
        const scoreField = `q${i}Score`;
        const commentField = `q${i}Comment`;

        if (response[scoreField] !== undefined && response[commentField] !== undefined) {
          completedQuestions++;
        }
      }

      // For ABI surveys, also check question 6 (NPS) - only needs score, no comment
      if (isABIOnAgency && response.q6Score !== undefined) {
        completedQuestions++;
      }

      const isComplete = completedQuestions === totalQuestions;
      const percentComplete = Math.round((completedQuestions / totalQuestions) * 100);

      progressMap[surveyId] = {
        completedQuestions,
        totalQuestions,
        isComplete,
        percentComplete
      };
    });

    return NextResponse.json({ progressMap });

  } catch (error) {
    console.error('Error fetching survey progress:', error);
    return NextResponse.json({ 
      error: 'Failed to fetch survey progress' 
    }, { status: 500 });
  }
} 
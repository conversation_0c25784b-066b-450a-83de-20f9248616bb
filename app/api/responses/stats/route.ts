import { NextRequest, NextResponse } from 'next/server';
import { getResponseStats } from '../../../../lib/api/responses';
import { monitorApiOperation } from '../../../../lib/performance';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const filters: Record<string, string> = {};
    
    // Extract filter parameters
    const agencyName = searchParams.get('agencyName');
    const brand = searchParams.get('brand');
    const region = searchParams.get('region');
    const country = searchParams.get('country');
    const assessmentType = searchParams.get('assessmentType');
    const wave = searchParams.get('wave');
    
    if (agencyName) filters.agencyName = agencyName;
    if (brand) filters.brand = brand;
    if (region) filters.region = region;
    if (country) filters.country = country;
    if (assessmentType) filters.assessmentType = assessmentType;
    if (wave) filters.wave = wave;
    
    const stats = await monitorApiOperation(
      'get-response-stats',
      () => getResponseStats(Object.keys(filters).length > 0 ? filters : undefined),
      { filtersCount: Object.keys(filters).length, filters }
    );
    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error fetching response stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch response stats' },
      { status: 500 }
    );
  }
} 
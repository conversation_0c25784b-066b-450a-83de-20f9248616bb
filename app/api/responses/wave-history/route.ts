import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '../../../../lib/mongodb';
import { ResponseModel } from '../../../../lib/models/response';
import { cache, withCache } from '../../../../lib/cache';

export async function GET(request: NextRequest) {
  try {
    await dbConnect();

    const { searchParams } = new URL(request.url);
    const focusType = searchParams.get('focusType') as 'agency' | 'brand' | 'region' | 'wave';
    const focusValue = searchParams.get('focusValue');

    if (!focusType || !focusValue) {
      return NextResponse.json(
        { error: 'focusType and focusValue are required' },
        { status: 400 }
      );
    }

    // Generate cache key for this request
    const cacheKey = cache.generateKey('wave-history', { focusType, focusValue });

    // Use caching for this expensive operation (5 minute TTL)
    const results = await withCache(cacheKey, async () => {

    // Build aggregation pipeline
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const pipeline: any[] = [
      // Join with surveys to get agency/brand/region info
      {
        $lookup: {
          from: 'surveys',
          localField: 'surveyId',
          foreignField: '_id',
          as: 'survey'
        }
      },
      { $unwind: '$survey' },
      // Join with waves to get wave info and filter out seed data
      {
        $lookup: {
          from: 'waves',
          localField: 'survey.waveId',
          foreignField: '_id',
          as: 'wave'
        }
      },
      { $unwind: '$wave' },
      // Filter out seed waves
      {
        $match: {
          'wave.status': { $ne: 'seed' }
        }
      }
    ];

    // Add focus filter
    const focusFilterCondition: Record<string, unknown> = {};
    if (focusType === 'agency') {
      focusFilterCondition['survey.agencyName'] = focusValue;
    } else if (focusType === 'brand') {
      focusFilterCondition['survey.brand'] = focusValue;
    } else if (focusType === 'region') {
      focusFilterCondition['survey.country'] = focusValue;
    } else if (focusType === 'wave') {
      // For wave focus, we want to show history across all agencies/brands/regions for that wave
      focusFilterCondition['wave.name'] = focusValue;
    }

    pipeline.push({ $match: focusFilterCondition });

    // Group by wave and assessment type to calculate separate scores
    pipeline.push({
      $group: {
        _id: {
          waveName: '$wave.name',
          assessmentType: '$survey.assessmentType'
        },
        averageScore: {
          $avg: {
            $avg: ['$q1Score', '$q2Score', '$q3Score', '$q4Score', '$q5Score']
          }
        },
        // Calculate NPS dynamically from q6Score values
        promoters: {
          $sum: {
            $cond: [
              { $and: [{ $gte: ['$q6Score', 9] }, { $lte: ['$q6Score', 10] }] },
              1,
              0
            ]
          }
        },
        detractors: {
          $sum: {
            $cond: [
              { $and: [{ $gte: ['$q6Score', 1] }, { $lte: ['$q6Score', 6] }] },
              1,
              0
            ]
          }
        },
        totalNpsResponses: {
          $sum: {
            $cond: [
              { $and: [{ $gte: ['$q6Score', 1] }, { $lte: ['$q6Score', 10] }] },
              1,
              0
            ]
          }
        },
        // Use wave creation date for chronological ordering (now set to reporting month)
        waveCreatedAt: { $first: '$wave.createdAt' },
        responseCount: { $sum: 1 }
      }
    });

    // Sort by wave creation date to get chronological order based on reporting months
    pipeline.push({ $sort: { waveCreatedAt: 1 } });

    // Format output
    pipeline.push({
      $project: {
        _id: 0,
        waveName: '$_id.waveName',
        assessmentType: '$_id.assessmentType',
        averageScore: { $round: ['$averageScore', 1] },
        // Calculate NPS: (% Promoters - % Detractors) normalized to -1 to 1 scale
        averageNPS: {
          $cond: [
            { $gt: ['$totalNpsResponses', 0] },
            {
              $round: [
                {
                  $subtract: [
                    { $divide: ['$promoters', '$totalNpsResponses'] },
                    { $divide: ['$detractors', '$totalNpsResponses'] }
                  ]
                },
                2
              ]
            },
            null
          ]
        },
        responseCount: 1,
        waveCreatedAt: 1
      }
    });

      return await ResponseModel.aggregate(pipeline);
    }, 5); // 5 minute cache

    // If no data found, return empty arrays
    if (results.length === 0) {
      return NextResponse.json({
        waveNames: [],
        abiOnAgency: { scores: [], npsScores: [], responseCounts: [] },
        agencyOnAbi: { scores: [], npsScores: [], responseCounts: [] },
        message: `No wave history data available for ${focusValue}.`
      });
    }

    // Get unique wave names in chronological order
    const uniqueWaves = [...new Set(results.map(r => r.waveName))];

    // Separate data by assessment type
    const abiOnAgencyData = results.filter(r => r.assessmentType === 'Anheuser Busch In-Bev-on-Agency');
    const agencyOnAbiData = results.filter(r => r.assessmentType === 'Agency-on-Anheuser Busch In-Bev');

    // Create arrays aligned with wave names
    const abiOnAgencyScores = uniqueWaves.map(waveName => {
      const data = abiOnAgencyData.find(r => r.waveName === waveName);
      return data ? data.averageScore : null;
    });

    const abiOnAgencyNPS = uniqueWaves.map(waveName => {
      const data = abiOnAgencyData.find(r => r.waveName === waveName);
      return data ? data.averageNPS : null;
    });

    const abiOnAgencyResponseCounts = uniqueWaves.map(waveName => {
      const data = abiOnAgencyData.find(r => r.waveName === waveName);
      return data ? data.responseCount : 0;
    });

    const agencyOnAbiScores = uniqueWaves.map(waveName => {
      const data = agencyOnAbiData.find(r => r.waveName === waveName);
      return data ? data.averageScore : null;
    });

    const agencyOnAbiNPS = uniqueWaves.map(waveName => {
      const data = agencyOnAbiData.find(r => r.waveName === waveName);
      return data ? data.averageNPS : null;
    });

    const agencyOnAbiResponseCounts = uniqueWaves.map(waveName => {
      const data = agencyOnAbiData.find(r => r.waveName === waveName);
      return data ? data.responseCount : 0;
    });

    return NextResponse.json({
      waveNames: uniqueWaves,
      abiOnAgency: {
        scores: abiOnAgencyScores,
        npsScores: abiOnAgencyNPS,
        responseCounts: abiOnAgencyResponseCounts
      },
      agencyOnAbi: {
        scores: agencyOnAbiScores,
        npsScores: agencyOnAbiNPS,
        responseCounts: agencyOnAbiResponseCounts
      },
      responseCount: results.reduce((sum, r) => sum + r.responseCount, 0)
    });

  } catch (error) {
    console.error('Error fetching wave history:', error);
    return NextResponse.json(
      { error: 'Failed to fetch wave history' },
      { status: 500 }
    );
  }
} 
import { NextRequest, NextResponse } from 'next/server';
import { performanceMonitor } from '../../../../lib/performance';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const operation = searchParams.get('operation') || undefined;

    const stats = performanceMonitor.getStats(operation);



    return NextResponse.json({
      success: true,
      stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error fetching performance stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch performance stats' },
      { status: 500 }
    );
  }
}

export async function DELETE() {
  try {
    performanceMonitor.clear();
    
    return NextResponse.json({
      success: true,
      message: 'Performance metrics cleared'
    });
  } catch (error) {
    console.error('Error clearing performance stats:', error);
    return NextResponse.json(
      { error: 'Failed to clear performance stats' },
      { status: 500 }
    );
  }
}

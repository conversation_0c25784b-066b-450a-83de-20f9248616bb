import { NextResponse } from 'next/server';
import { monitorApiOperation } from '../../../lib/performance';

export async function GET() {
  try {

    
    // Test a simple operation
    const result = await monitorApiOperation(
      'test-simple-operation',
      async () => {
        // Simulate some work
        await new Promise(resolve => setTimeout(resolve, 100));
        return { message: 'Test operation completed' };
      },
      { testData: 'simple test' }
    );

    // Test a slower operation
    const slowResult = await monitorApiOperation(
      'test-slow-operation',
      async () => {
        // Simulate slower work
        await new Promise(resolve => setTimeout(resolve, 1200));
        return { message: 'Slow operation completed' };
      },
      { testData: 'slow test' }
    );

    return NextResponse.json({
      success: true,
      message: 'Performance test completed',
      results: [result, slowResult]
    });
  } catch (error) {
    console.error('Test performance error:', error);
    return NextResponse.json(
      { error: 'Test failed' },
      { status: 500 }
    );
  }
}

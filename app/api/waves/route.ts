import { NextRequest, NextResponse } from 'next/server';
import { getWaveVms, getDashboardWaveVms, addWave } from '../../../lib/api/waves';
import { IWaveInput } from '../../../lib/models/wave';
import { ISurveyImport } from '../../../lib/models/survey';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const excludeSeed = searchParams.get('excludeSeed') === 'true';

    const waves = excludeSeed ? await getDashboardWaveVms() : await getWaveVms();
    return NextResponse.json(waves);
  } catch (error) {
    console.error('Error fetching waves:', error);
    return NextResponse.json(
      { error: 'Failed to fetch waves' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { wave, surveyImports }: { wave: IWaveInput; surveyImports: ISurveyImport[] } = body;

    // Validate required fields
    if (!wave.name || typeof wave.name !== 'string' || !wave.name.trim()) {
      return NextResponse.json(
        { error: 'Wave name is required' },
        { status: 400 }
      );
    }

    if (!surveyImports || !Array.isArray(surveyImports) || surveyImports.length === 0) {
      return NextResponse.json(
        { error: 'Survey imports are required' },
        { status: 400 }
      );
    }

    const result = await addWave(wave, surveyImports);

    return NextResponse.json({
      success: true,
      message: `Seed data "${wave.name}" created successfully with ${surveyImports.length} surveys!`,
      waveId: result.waveId,
    });
  } catch (error: unknown) {
    console.error('Error creating wave:', error);

    // Handle both Error objects and string rejections from Promise.reject()
    const errorMessage = error instanceof Error ? error.message : String(error);

    // Handle specific error cases with user-friendly messages
    if (errorMessage === 'Name already exists') {
      return NextResponse.json(
        { error: 'A wave with this name already exists. Please choose a different name.' },
        { status: 400 }
      );
    }

    if (errorMessage === 'Name is required') {
      return NextResponse.json(
        { error: 'Wave name is required' },
        { status: 400 }
      );
    }

    if (errorMessage === 'No surveys provided') {
      return NextResponse.json(
        { error: 'Survey imports are required' },
        { status: 400 }
      );
    }

    // Log the actual error message for debugging
    console.error('Unhandled error message:', errorMessage);

    // Generic error for unexpected issues
    return NextResponse.json(
      { error: 'Failed to create wave. Please try again.' },
      { status: 500 }
    );
  }
}
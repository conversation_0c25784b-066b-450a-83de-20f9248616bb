import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '../../../../../lib/mongodb';
import { SurveyModel } from '../../../../../lib/models/survey';
import { sendReminderMessages } from '../../../../../lib/mailgun';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ waveId: string }> }
) {
  try {
    await dbConnect();

    const { waveId } = await params;

    // Get all surveys for this wave that are not completed and not removed
    const incompleteSurveys = await SurveyModel.find({
      waveId: waveId,
      isRemoved: { $ne: true }, // Exclude removed surveys
      $or: [
        { surveyStatus: 'not_started' },
        { surveyStatus: 'in_progress' },
        { surveyStatus: { $exists: false } }, // Handle surveys without status
        { surveyStatus: null }
      ]
    });

    if (incompleteSurveys.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No incomplete surveys found to send reminders for',
        stats: {
          remindersSent: 0,
          uniqueEmails: 0
        }
      });
    }

    // Get unique emails to count how many people will receive reminders
    const uniqueEmails = new Set(
      incompleteSurveys
        .map(survey => survey.userEmail)
        .filter(email => email && email.trim())
    );

    // Send reminder emails
    await sendReminderMessages(incompleteSurveys);

    return NextResponse.json({
      success: true,
      message: `Reminder emails sent successfully to ${uniqueEmails.size} recipients for ${incompleteSurveys.length} incomplete surveys`,
      stats: {
        remindersSent: incompleteSurveys.length,
        uniqueEmails: uniqueEmails.size
      }
    });

  } catch (error) {
    console.error('Error sending reminder emails:', error);
    return NextResponse.json(
      { error: 'Failed to send reminder emails' },
      { status: 500 }
    );
  }
}

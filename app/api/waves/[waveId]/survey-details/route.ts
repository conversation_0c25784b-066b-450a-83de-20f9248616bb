import { NextRequest, NextResponse } from 'next/server';
import { Types } from 'mongoose';
import dbConnect from '../../../../../lib/mongodb';
import { SurveyModel } from '../../../../../lib/models/survey';
import { ResponseModel } from '../../../../../lib/models/response';
import { UserPasscodeModel } from '../../../../../lib/models/user-passcode';
import { WaveModel } from '../../../../../lib/models/wave';

export interface ISurveyDetail {
  id: string;
  userName: string;
  userEmail: string;
  agencyName: string;
  agencyType: string;
  brand: string;
  country: string;
  region: string;
  assessmentType: string;
  accountName: string;
  userStatus: string;
  inScope: string;
  notes: string;
  passcode?: string;
  quickLoginUrl?: string;
  surveyStatus: 'completed' | 'in_progress' | 'not_started';
  responseCount: number;
  totalQuestions: number;
  completionPercentage: number;
  lastActivity?: string;
  isRemoved?: boolean;
  createdAt: string;
  updatedAt: string;
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ waveId: string }> }
) {
  try {
    await dbConnect();

    const { waveId } = await params;
    const waveObjectId = Types.ObjectId.createFromHexString(waveId);

    // Verify wave exists
    const wave = await WaveModel.findById(waveObjectId);
    if (!wave) {
      return NextResponse.json(
        { error: 'Wave not found' },
        { status: 404 }
      );
    }

    // Get all surveys for this wave
    const surveys = await SurveyModel.find({ waveId: waveObjectId });

    // Get all responses for surveys in this wave
    const surveyIds = surveys.map(s => s._id);
    const responses = await ResponseModel.find({ 
      surveyId: { $in: surveyIds } 
    });

    // Get all passcodes for this wave
    const passcodes = await UserPasscodeModel.find({ waveId: waveObjectId });
    const passcodeMap = new Map(
      passcodes.map(p => [p.userEmail.toLowerCase(), p])
    );

    // Build survey details
    const surveyDetails: ISurveyDetail[] = await Promise.all(
      surveys.map(async (survey) => {
        // Find responses for this survey
        const surveyResponses = responses.filter(r => 
          r.surveyId.toString() === survey._id.toString()
        );

        // Calculate total questions based on assessment type
        const totalQuestions = survey.assessmentType === 'ABI-assess-Agency' ? 6 : 5;
        
        // Calculate completion status
        let surveyStatus: 'completed' | 'in_progress' | 'not_started' = 'not_started';
        let completionPercentage = 0;
        const responseCount = surveyResponses.length;
        let lastActivity: string | undefined;

        if (surveyResponses.length > 0) {
          // Check if survey is complete (has response with all required fields)
          const latestResponse = surveyResponses[surveyResponses.length - 1];

          // Count questions that have been answered (including N/A responses stored as null)
          // A question is considered "answered" if the field exists and is not undefined
          const answeredQuestions = [
            latestResponse.q1Score,
            latestResponse.q2Score,
            latestResponse.q3Score,
            latestResponse.q4Score,
            latestResponse.q5Score,
            ...(survey.assessmentType === 'ABI-assess-Agency' ? [latestResponse.q6Score] : [])
          ].filter(score => score !== undefined).length; // Allow null (N/A) but not undefined

          completionPercentage = Math.round((answeredQuestions / totalQuestions) * 100);
          
          if (completionPercentage === 100) {
            surveyStatus = 'completed';
          } else if (completionPercentage > 0) {
            surveyStatus = 'in_progress';
          }

          lastActivity = latestResponse.updatedAt?.toISOString();
        }

        // Get passcode info
        const userPasscode = passcodeMap.get(survey.userEmail.toLowerCase());
        const passcode = userPasscode?.passcode;
        const quickLoginUrl = passcode ? 
          `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/dashboard?passcode=${passcode}` : 
          undefined;

        return {
          id: survey._id.toString(),
          userName: survey.userName,
          userEmail: survey.userEmail,
          agencyName: survey.agencyName,
          agencyType: survey.agencyType,
          brand: survey.brand,
          country: survey.country,
          region: survey.region,
          assessmentType: survey.assessmentType,
          accountName: survey.accountName,
          userStatus: survey.userStatus,
          inScope: survey.inScope,
          notes: survey.notes || '',
          passcode,
          quickLoginUrl,
          surveyStatus,
          responseCount,
          totalQuestions,
          completionPercentage,
          lastActivity,
          isRemoved: survey.isRemoved || false,
          createdAt: survey.createdAt.toISOString(),
          updatedAt: survey.updatedAt.toISOString(),
        };
      })
    );

    return NextResponse.json(surveyDetails);

  } catch (error) {
    console.error('Error fetching survey details:', error);
    return NextResponse.json(
      { error: 'Failed to fetch survey details' },
      { status: 500 }
    );
  }
}

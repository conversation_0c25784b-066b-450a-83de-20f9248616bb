'use client';

import { ChangeEvent, useState, useRef, useEffect } from 'react';
import { IResponseImport, importResponses } from '../../../lib/api/responses';
import { parse, ParseResult } from 'papaparse';
import { expectedResponseHeaders } from '../../_constants/expected-seed-headers';
import {
	Box,
	Typography,
	Button,
	Input,
	Alert,
	Chip,
	Divider,
	CircularProgress,
	InputLabel,
	Select,
	MenuItem,
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import DashboardPanel from '../dashboard/DashboardPanel';
import PanelHeader from '../dashboard/PanelHeader';
import PanelSubheader from '../dashboard/PanelSubheader';
import ElevatedFormControl from '../../_components/ElevatedFormControl';

interface ImportResponsesClientProps {
	pageTitle?: string;
}

interface Wave {
	id: string;
	name: string;
	status: string;
	surveyCount?: number;
}

const ImportResponsesClient = ({
	pageTitle = 'Import Responses',
}: ImportResponsesClientProps) => {
	const theme = useTheme();
	const [importFilename, setImportFilename] = useState<string>('');
	const [responseImports, setResponseImports] = useState<IResponseImport[]>([]);
	const [importError, setImportError] = useState<string>('');
	const [isImporting, setIsImporting] = useState<boolean>(false);
	const [importResults, setImportResults] = useState<{
		imported: number;
		errors: string[];
		created: {
			waves: number;
			surveys: number;
		};
	} | null>(null);
	const [clearing, setClearing] = useState(false);
	const [clearResult, setClearResult] = useState<{
		deletedResponses: number;
		deletedSurveys: number;
		deletedPasscodes: number;
	} | null>(null);
	const [waves, setWaves] = useState<Wave[]>([]);
	const [selectedWave, setSelectedWave] = useState<string>('');
	const [loadingWaves, setLoadingWaves] = useState(true);
	const fileInputRef = useRef<HTMLInputElement>(null);

	// Fetch available waves on component mount
	useEffect(() => {
		const fetchWaves = async () => {
			try {
				setLoadingWaves(true);
				const response = await fetch('/api/waves');
				if (response.ok) {
					const wavesData = await response.json();
					setWaves(wavesData);
					// Auto-select the first wave if available
					if (wavesData.length > 0) {
						setSelectedWave(wavesData[0].name);
					}
				} else {
					console.error('Failed to fetch waves');
				}
			} catch (error) {
				console.error('Error fetching waves:', error);
			} finally {
				setLoadingWaves(false);
			}
		};

		fetchWaves();
	}, []);

	const clearSelectedFileOnClick = () => {
		setImportFilename('');
		setResponseImports([]);
		setImportError('');
		setImportResults(null);
	};

	const importResponsesOnClick = async () => {
		setImportError('');
		setIsImporting(true);
		setImportResults(null);

		try {
			const results = await importResponses(responseImports);
			setImportResults(results);
		} catch (error: unknown) {
			const errorMessage =
				error instanceof Error ? error.message : 'An unknown error occurred';
			setImportError(errorMessage);
		}

		setIsImporting(false);
	};

	const processFile = (file: File): void => {
		setImportError('');
		setImportResults(null);

		const complete = async (results: ParseResult<string[]>): Promise<void> => {
			const headerLocations = expectedResponseHeaders.map((header) =>
				results.data[0].indexOf(header)
			);

			if (headerLocations.includes(-1)) {
				setImportError(
					`CSV must contain headers: ${expectedResponseHeaders.join(', ')}`
				);
				return;
			}

			// Build response imports while collecting row-level validation errors with line numbers
			const newResponseImports: IResponseImport[] = [];
			const rowErrors: string[] = [];

			results.data.slice(1).forEach((row: string[], index: number) => {
				// CSV data row number (accounting for header at line 1)
				const csvLineNumber = index + 2;

				// Skip completely empty rows
				const isEmptyRow = !row.some((cell) => cell.trim() !== '');
				if (isEmptyRow) return;

				const agency = row[headerLocations[0]]?.trim() ?? '';
				const type = row[headerLocations[1]] ?? '';
				const zone = row[headerLocations[2]] ?? '';
				const country = row[headerLocations[3]] ?? '';
				const brand = row[headerLocations[4]] ?? '';
				const brandCategory = row[headerLocations[5]] ?? '';
				const reportingMonth = row[headerLocations[6]] ?? '';
				const account = row[headerLocations[7]] ?? '';
				const assessorName = row[headerLocations[8]]?.trim() ?? '';
				const assessorType = row[headerLocations[9]] ?? '';
				const section = row[headerLocations[10]]?.trim() ?? '';
				const scoreRaw = row[headerLocations[11]] ?? '';
				const comment = row[headerLocations[12]] || '';
				const evaluationName = row[headerLocations[13]] ?? '';
				const netPromoterScoreRaw = row[headerLocations[14]] ?? '';

				const parsedScore = parseFloat(scoreRaw);

				// Parse NPS score (optional, only for ABI-on-Agency assessments)
				let parsedNpsScore: number | undefined;
				if (netPromoterScoreRaw && netPromoterScoreRaw.trim() !== '') {
					const npsValue = parseFloat(netPromoterScoreRaw);
					if (!Number.isNaN(npsValue) && npsValue >= 1 && npsValue <= 10) {
						parsedNpsScore = npsValue;
					}
				}

				const reasons: string[] = [];
				if (!agency) reasons.push('missing Agency');
				if (!assessorName) reasons.push('missing Assessor Name');
				if (!section) reasons.push('missing Section');
				if (!scoreRaw || Number.isNaN(parsedScore) || parsedScore === 0)
					reasons.push(
						`invalid Score '${scoreRaw || ''}' (must be a number > 0)`
					);

				if (reasons.length > 0) {
					rowErrors.push(`Row ${csvLineNumber}: ${reasons.join('; ')}`);
					return;
				}

				newResponseImports.push({
					agency,
					type,
					zone,
					country,
					brand,
					brandCategory,
					reportingMonth,
					account,
					assessorName,
					assessorType,
					section,
					score: parsedScore,
					comment,
					evaluationName,
					netPromoterScore: parsedNpsScore,
				});
			});

			if (rowErrors.length > 0) {
				const previewCount = 10;
				const preview = rowErrors.slice(0, previewCount).join(' | ');
				const remaining = rowErrors.length - previewCount;
				const suffix = remaining > 0 ? ` | ... and ${remaining} more` : '';
				setImportError(
					`CSV contains ${rowErrors.length} row(s) with missing/invalid required fields (Agency, Assessor Name, Section, Score). ${preview}${suffix}`
				);
				return;
			}

			setImportFilename(file.name);
			setResponseImports(newResponseImports);
		};

		const error = (error: Error): void => {
			setImportError(`Parsing error: ${error.message}`);
		};

		parse<string[]>(file, { complete, error });
	};

	// Handle file input change
	const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
		if (e.target.files && e.target.files[0]) {
			processFile(e.target.files[0]);
		}
	};

	// Handle drag and drop
	const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
		e.preventDefault();
		if (e.dataTransfer.files && e.dataTransfer.files[0]) {
			processFile(e.dataTransfer.files[0]);
		}
	};

	const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
		e.preventDefault();
	};

	const handleClearWave = async () => {
		if (!selectedWave) {
			alert('Please select a wave to clear');
			return;
		}

		if (
			!confirm(
				`Are you sure you want to clear all data for wave "${selectedWave}"? This cannot be undone.`
			)
		) {
			return;
		}

		setClearing(true);
		setClearResult(null);

		try {
			const response = await fetch('/api/responses/clear-wave', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ waveName: selectedWave }),
			});

			if (response.ok) {
				const data = await response.json();
				setClearResult(data);
				// Refresh waves list after clearing
				const wavesResponse = await fetch('/api/waves');
				if (wavesResponse.ok) {
					const wavesData = await wavesResponse.json();
					setWaves(wavesData);
					// Reset selected wave if the cleared wave was selected
					if (wavesData.length > 0) {
						setSelectedWave(wavesData[0].name);
					} else {
						setSelectedWave('');
					}
				}
			} else {
				alert('Failed to clear wave data');
			}
		} catch (error) {
			console.error('Error clearing wave:', error);
			alert('Error clearing wave data');
		} finally {
			setClearing(false);
		}
	};

	return (
		<Box>
			<Typography
				variant="h4"
				fontWeight={700}
				color="text.primary"
				gutterBottom
			>
				{pageTitle}
			</Typography>
			<PanelSubheader gutterBottom sx={{ mb: 4 }}>
				Import survey responses from CSV file. Waves and surveys will be created
				automatically if they don&apos;t exist. Responses will be ordered by
				their &quot;Reporting Month&quot; column for proper chronological
				display.
			</PanelSubheader>

			<DashboardPanel sx={{ maxWidth: 600, mb: 4 }}>
				<PanelHeader gutterBottom>Select Response Data (CSV)</PanelHeader>

				{importFilename ? (
					<Box sx={{ mb: 2 }}>
						<PanelSubheader sx={{ mb: 1 }}>
							Response file selected.
						</PanelSubheader>
						<PanelSubheader sx={{ mb: 1 }}>
							{`${responseImports.length} total response records from ${importFilename}`}
						</PanelSubheader>
						<Button
							variant="outlined"
							color="secondary"
							onClick={clearSelectedFileOnClick}
							sx={{ mb: 2 }}
						>
							Choose different file
						</Button>
					</Box>
				) : (
					<Box
						onDrop={handleDrop}
						onDragOver={handleDragOver}
						sx={{
							border: `2px dashed ${theme.palette.mode === 'dark' ? '#06406F' : '#90caf9'
								}`,
							borderRadius: 2,
							p: 3,
							textAlign: 'center',
							mb: 2,
							bgcolor: theme.palette.mode === 'dark' ? '#020609' : '#f5fafd',
							cursor: 'pointer',
						}}
						onClick={() => fileInputRef.current?.click()}
					>
						<Input
							inputRef={fileInputRef}
							type="file"
							inputProps={{ accept: '.csv' }}
							onChange={handleFileChange}
							sx={{ display: 'none' }}
						/>
						<PanelSubheader>
							Drag and drop a CSV file here, or click to browse
						</PanelSubheader>
					</Box>
				)}

				{importError && (
					<Alert severity="error" sx={{ mb: 2 }}>
						{importError}
					</Alert>
				)}

				{importResults && (
					<Box sx={{ mb: 2 }}>
						<Alert severity="success" sx={{ mb: 2 }}>
							<Typography variant="subtitle2" gutterBottom>
								Import completed successfully!
							</Typography>
							<Typography variant="body2">
								• {importResults.imported} responses imported
							</Typography>
							{importResults.created.waves > 0 && (
								<Typography variant="body2">
									• {importResults.created.waves} waves created automatically
								</Typography>
							)}
							{importResults.created.surveys > 0 && (
								<Typography variant="body2">
									• {importResults.created.surveys} surveys created
									automatically
								</Typography>
							)}
						</Alert>

						{importResults.errors.length > 0 && (
							<Alert severity="warning" sx={{ mb: 2 }}>
								<Typography variant="subtitle2" gutterBottom>
									{importResults.errors.length} errors occurred:
								</Typography>
								<Box sx={{ maxHeight: 200, overflow: 'auto' }}>
									{importResults.errors.map((error, index) => (
										<Typography key={index} variant="body2" sx={{ mb: 0.5 }}>
											• {error}
										</Typography>
									))}
								</Box>
							</Alert>
						)}
					</Box>
				)}

				<Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
					<Button
						variant="contained"
						color="primary"
						onClick={importResponsesOnClick}
						disabled={!responseImports.length || isImporting}
					>
						Import Responses
					</Button>
					{isImporting && <CircularProgress size={24} />}
				</Box>
			</DashboardPanel>

			{responseImports.length > 0 && (
				<DashboardPanel sx={{ maxWidth: 600, mb: 4 }}>
					<PanelHeader gutterBottom>Preview Data</PanelHeader>
					<PanelSubheader sx={{ mb: 2 }}>
						Showing first few records from your CSV:
					</PanelSubheader>

					{responseImports.slice(0, 3).map((response, index) => (
						<Box
							key={index}
							sx={{ mb: 2, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}
						>
							<Box sx={{ display: 'flex', gap: 1, mb: 1, flexWrap: 'wrap' }}>
								<Chip label={response.agency} size="small" />
								<Chip label={response.brand} size="small" color="primary" />
								<Chip
									label={response.assessorName}
									size="small"
									color="secondary"
								/>
								<Chip
									label={response.reportingMonth}
									size="small"
									variant="outlined"
								/>
							</Box>
							<Typography variant="body2">
								{response.section}: {response.score}/5
							</Typography>
							{response.comment && (
								<Typography
									variant="body2"
									color="text.secondary"
									sx={{ fontStyle: 'italic' }}
								>
									&quot;{response.comment.substring(0, 100)}
									{response.comment.length > 100 ? '...' : ''}&quot;
								</Typography>
							)}
						</Box>
					))}

					{responseImports.length > 3 && (
						<PanelSubheader>
							... and {responseImports.length - 3} more records
						</PanelSubheader>
					)}
				</DashboardPanel>
			)}

			<Divider sx={{ my: 4 }} />

			<DashboardPanel sx={{ maxWidth: 600 }}>
				<PanelHeader gutterBottom>Clear Wave Data</PanelHeader>
				<PanelSubheader sx={{ mb: 2 }}>
					Select a wave and clear all its data (responses, surveys, user
					passcodes, and the wave itself).
				</PanelSubheader>

				<Box sx={{ mb: 2 }}>
					<ElevatedFormControl fullWidth size="small" disabled={loadingWaves}>
						<InputLabel>Select Wave to Clear</InputLabel>
						<Select
							value={selectedWave}
							label="Select Wave to Clear"
							onChange={(e) => setSelectedWave(e.target.value)}
						>
							{waves.map((wave) => (
								<MenuItem key={wave.id} value={wave.name}>
									{wave.name} ({wave.surveyCount || 0} surveys, {wave.status})
								</MenuItem>
							))}
						</Select>
					</ElevatedFormControl>
					{loadingWaves && (
						<PanelSubheader sx={{ mt: 1 }}>Loading waves...</PanelSubheader>
					)}
					{!loadingWaves && waves.length === 0 && (
						<PanelSubheader sx={{ mt: 1 }}>No waves found</PanelSubheader>
					)}
				</Box>

				<Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
					<Button
						variant="contained"
						color="error"
						onClick={handleClearWave}
						disabled={clearing || !selectedWave || loadingWaves}
					>
						Clear Selected Wave
					</Button>
					{clearing && <CircularProgress size={24} />}
				</Box>

				{clearResult && (
					<Box sx={{ mt: 2 }}>
						<Alert severity="success" sx={{ mb: 2 }}>
							<Typography variant="subtitle2" gutterBottom>
								Wave data cleared successfully!
							</Typography>
							<Typography variant="body2">
								• {clearResult.deletedResponses} responses deleted
							</Typography>
							{clearResult.deletedSurveys > 0 && (
								<Typography variant="body2">
									• {clearResult.deletedSurveys} surveys deleted
								</Typography>
							)}
							{clearResult.deletedPasscodes > 0 && (
								<Typography variant="body2">
									• {clearResult.deletedPasscodes} user passcodes deleted
								</Typography>
							)}
						</Alert>
					</Box>
				)}
			</DashboardPanel>
		</Box>
	);
};

export default ImportResponsesClient;

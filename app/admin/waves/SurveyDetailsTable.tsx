'use client';

import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TableSortLabel,
  Typography,
  Button,
  Box,
  Chip,
  useTheme,
  IconButton,
  Tooltip,
  Stack,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from '@mui/material';
import {
  Edit as EditIcon,
  Email as EmailIcon,
  Notifications as NotificationsIcon,
  Delete as DeleteIcon,
  Restore as RestoreIcon,
} from '@mui/icons-material';
import {
  ISurveyDetail,
  getAvailableActions,
  getSurveyStatusColor,
  formatSurveyStatus,
  formatLastActivity,
  SurveyActionType,
} from '../../../lib/types/survey-details';

interface SurveyDetailsTableProps {
  surveys: ISurveyDetail[];
  loading?: boolean;
  onAction?: (surveyId: string, action: SurveyActionType, data?: Record<string, unknown>) => Promise<void>;
}

type Order = 'asc' | 'desc';

const columns = [
  { id: 'userName', label: 'Name' },
  { id: 'userEmail', label: 'Email' },
  { id: 'passcode', label: 'Passcode' },
  { id: 'agencyName', label: 'Agency' },
  { id: 'brand', label: 'Brand' },
  { id: 'region', label: 'Region' },
  { id: 'surveyStatus', label: 'Status' },
  { id: 'completionPercentage', label: 'Progress' },
  { id: 'lastActivity', label: 'Last Activity' },
  { id: 'actions', label: 'Actions' },
];

function descendingComparator(a: ISurveyDetail, b: ISurveyDetail, orderBy: string) {
  let aValue: string | number | Date = a[orderBy as keyof ISurveyDetail] as string | number | Date;
  let bValue: string | number | Date = b[orderBy as keyof ISurveyDetail] as string | number | Date;

  // Handle special cases
  if (orderBy === 'lastActivity') {
    aValue = a.lastActivity ? new Date(a.lastActivity).getTime() : 0;
    bValue = b.lastActivity ? new Date(b.lastActivity).getTime() : 0;
  }

  // Convert to comparable values
  const aComp = typeof aValue === 'string' ? aValue.toLowerCase() : aValue;
  const bComp = typeof bValue === 'string' ? bValue.toLowerCase() : bValue;

  if (bComp < aComp) return -1;
  if (bComp > aComp) return 1;
  return 0;
}

function getComparator(order: Order, orderBy: string) {
  return order === 'desc'
    ? (a: ISurveyDetail, b: ISurveyDetail) => descendingComparator(a, b, orderBy)
    : (a: ISurveyDetail, b: ISurveyDetail) => -descendingComparator(a, b, orderBy);
}

interface SeedDataOptions {
  regions: string[];
  brands: string[];
  agencies: string[];
}

export default function SurveyDetailsTable({
  surveys,
  loading = false,
  onAction
}: SurveyDetailsTableProps) {
  const [order, setOrder] = useState<Order>('asc');
  const [orderBy, setOrderBy] = useState<string>('userName');
  const [editingSurvey, setEditingSurvey] = useState<ISurveyDetail | null>(null);
  const [editFormData, setEditFormData] = useState<Partial<ISurveyDetail>>({});
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [seedDataOptions, setSeedDataOptions] = useState<SeedDataOptions | null>(null);
  const [loadingSeedData, setLoadingSeedData] = useState(false);
  const theme = useTheme();

  const handleSort = (property: string) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const handleAction = async (surveyId: string, action: SurveyActionType, data?: Record<string, unknown>) => {
    if (!onAction) return;
    
    setActionLoading(surveyId);
    try {
      await onAction(surveyId, action, data);
    } finally {
      setActionLoading(null);
    }
  };

  // Fetch seed data options for dropdowns
  const fetchSeedDataOptions = async () => {
    if (seedDataOptions) return; // Already loaded

    setLoadingSeedData(true);
    try {
      // Get the latest seed data (this should match what was used when the wave was created)
      const response = await fetch('/api/launch-wave/seed-data');
      const data = await response.json();

      if (data.hasData && data.data) {
        setSeedDataOptions({
          regions: data.data.regions,
          brands: data.data.brands,
          agencies: data.data.agencies,
        });
      }
    } catch (error) {
      console.error('Error fetching seed data options:', error);
    } finally {
      setLoadingSeedData(false);
    }
  };

  const handleEdit = (survey: ISurveyDetail) => {
    setEditingSurvey(survey);
    setEditFormData({
      userName: survey.userName,
      userEmail: survey.userEmail,
      agencyName: survey.agencyName,
      brand: survey.brand,
      country: survey.country,
      region: survey.region,
      notes: survey.notes,
    });

    // Fetch seed data options when opening edit dialog
    fetchSeedDataOptions();
  };

  const handleSaveEdit = async () => {
    if (!editingSurvey || !onAction) return;

    await handleAction(editingSurvey.id, 'save', editFormData);
    setEditingSurvey(null);
    setEditFormData({});
  };

  const handleCancelEdit = () => {
    setEditingSurvey(null);
    setEditFormData({});
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSaveEdit();
    } else if (event.key === 'Escape') {
      event.preventDefault();
      handleCancelEdit();
    }
  };



  const sortedSurveys = [...surveys].sort(getComparator(order, orderBy));

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <>
      <TableContainer>
        <Table size="small">
          <TableHead>
            <TableRow>
              {columns.map((col) => (
                <TableCell key={col.id} sx={{ fontWeight: 700 }}>
                  {col.id !== 'actions' ? (
                    <TableSortLabel
                      active={orderBy === col.id}
                      direction={orderBy === col.id ? order : 'asc'}
                      onClick={() => handleSort(col.id)}
                    >
                      {col.label}
                    </TableSortLabel>
                  ) : (
                    col.label
                  )}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {sortedSurveys.map((survey, idx) => {
              const actions = getAvailableActions(survey);
              const isLoading = actionLoading === survey.id;
              
              return (
                <TableRow
                  key={survey.id}
                  sx={{
                    backgroundColor:
                      idx % 2 === 0
                        ? theme.palette.background.paper
                        : theme.palette.action.hover,
                    opacity: survey.isRemoved ? 0.6 : 1,
                    textDecoration: survey.isRemoved ? 'line-through' : 'none',
                    '& .MuiTableCell-root': {
                      textDecoration: survey.isRemoved ? 'line-through' : 'none',
                    },
                  }}
                >
                  <TableCell>{survey.userName}</TableCell>
                  <TableCell>{survey.userEmail}</TableCell>
                  <TableCell>
                    <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                      {survey.passcode || '-'}
                    </Typography>
                  </TableCell>
                  <TableCell>{survey.agencyName}</TableCell>
                  <TableCell>{survey.brand}</TableCell>
                  <TableCell>{survey.region}</TableCell>
                  <TableCell>
                    <Chip
                      label={formatSurveyStatus(survey.surveyStatus)}
                      size="small"
                      color={getSurveyStatusColor(survey.surveyStatus)}
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="body2">
                        {survey.completionPercentage}%
                      </Typography>
                      <Box
                        sx={{
                          width: 60,
                          height: 4,
                          backgroundColor: theme.palette.grey[300],
                          borderRadius: 2,
                          overflow: 'hidden',
                        }}
                      >
                        <Box
                          sx={{
                            width: `${survey.completionPercentage}%`,
                            height: '100%',
                            backgroundColor: theme.palette.primary.main,
                          }}
                        />
                      </Box>
                    </Box>
                  </TableCell>

                  <TableCell>
                    <Typography variant="body2">
                      {formatLastActivity(survey.lastActivity)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Stack direction="row" spacing={0.5}>
                      {actions.map((action) => (
                        <Tooltip key={action.id} title={action.label}>
                          <span>
                            <IconButton
                              size="small"
                              color={action.color}
                              disabled={action.disabled || isLoading}
                              onClick={() => {
                                if (action.id === 'edit') {
                                  handleEdit(survey);
                                } else {
                                  handleAction(survey.id, action.id as SurveyActionType);
                                }
                              }}
                              sx={{
                                border: `1px solid ${theme.palette[action.color].main}`,
                                '&:hover': {
                                  backgroundColor: `${theme.palette[action.color].main}20`,
                                },
                              }}
                            >
                              {isLoading ? (
                                <CircularProgress size={16} />
                              ) : (
                                action.id === 'edit' ? <EditIcon fontSize="small" /> :
                                action.id === 'resend_invite' ? <EmailIcon fontSize="small" /> :
                                action.id === 'send_reminder' ? <NotificationsIcon fontSize="small" /> :
                                action.id === 'remove' ? <DeleteIcon fontSize="small" /> :
                                action.id === 'restore' ? <RestoreIcon fontSize="small" /> : null
                              )}
                            </IconButton>
                          </span>
                        </Tooltip>
                      ))}
                    </Stack>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Edit Dialog */}
      <Dialog open={!!editingSurvey} onClose={handleCancelEdit} maxWidth="sm" fullWidth>
        <DialogTitle>Edit Survey Details</DialogTitle>
        <DialogContent>
          <Stack spacing={2} sx={{ mt: 1 }} onKeyDown={handleKeyDown}>
            <TextField
              label="Name"
              value={editFormData.userName || ''}
              onChange={(e) => setEditFormData({ ...editFormData, userName: e.target.value })}
              fullWidth
              onKeyDown={handleKeyDown}
            />
            <TextField
              label="Email"
              value={editFormData.userEmail || ''}
              onChange={(e) => setEditFormData({ ...editFormData, userEmail: e.target.value })}
              fullWidth
              onKeyDown={handleKeyDown}
            />
            <FormControl fullWidth>
              <InputLabel>Agency</InputLabel>
              <Select
                value={editFormData.agencyName || ''}
                label="Agency"
                onChange={(e) => setEditFormData({ ...editFormData, agencyName: e.target.value })}
                disabled={loadingSeedData}
                onKeyDown={handleKeyDown}
              >
                {seedDataOptions?.agencies.map((agency) => (
                  <MenuItem key={agency} value={agency}>
                    {agency}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl fullWidth>
              <InputLabel>Brand</InputLabel>
              <Select
                value={editFormData.brand || ''}
                label="Brand"
                onChange={(e) => setEditFormData({ ...editFormData, brand: e.target.value })}
                disabled={loadingSeedData}
                onKeyDown={handleKeyDown}
              >
                {seedDataOptions?.brands.map((brand) => (
                  <MenuItem key={brand} value={brand}>
                    {brand}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl fullWidth>
              <InputLabel>Region</InputLabel>
              <Select
                value={editFormData.region || ''}
                label="Region"
                onChange={(e) => setEditFormData({ ...editFormData, region: e.target.value })}
                disabled={loadingSeedData}
                onKeyDown={handleKeyDown}
              >
                {seedDataOptions?.regions.map((region) => (
                  <MenuItem key={region} value={region}>
                    {region}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <TextField
              label="Notes"
              value={editFormData.notes || ''}
              onChange={(e) => setEditFormData({ ...editFormData, notes: e.target.value })}
              multiline
              rows={3}
              fullWidth
              onKeyDown={(e) => {
                // For multiline text fields, only trigger save on Ctrl+Enter or Cmd+Enter
                if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                  e.preventDefault();
                  handleSaveEdit();
                } else if (e.key === 'Escape') {
                  e.preventDefault();
                  handleCancelEdit();
                }
              }}
            />
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelEdit}>Cancel</Button>
          <Button onClick={handleSaveEdit} variant="contained">
            Save Changes
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

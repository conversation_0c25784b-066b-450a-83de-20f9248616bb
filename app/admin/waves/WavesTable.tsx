'use client';

import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Button,
  Box,
  Chip,
  useTheme,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  CircularProgress,
  Stack,
  Collapse,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import DownloadIcon from '@mui/icons-material/Download';
import EmailIcon from '@mui/icons-material/Email';
import { useRouter } from 'next/navigation';
import { IWaveVm } from '../../../lib/api/waves';
import { useState, useEffect, useRef } from 'react';
import TableSortLabel from '@mui/material/TableSortLabel';
import DashboardPanel from '../dashboard/DashboardPanel';
import { formatDate } from '../../../util/format-date';
import SurveyDetailsTable from './SurveyDetailsTable';
import { ISurveyDetail, SurveyActionType, formatSurveyStatus } from '../../../lib/types/survey-details';

interface WavesTableProps {
  waveVms: IWaveVm[];
}

interface WaveStats {
  isSeedWave?: boolean;
  totalResponses: number;
  totalSurveys: number;
  completionRate: number;
  averageScore: number;
  averageNPS: number;
  agencyCount: number;
  brandCount: number;
  regionCount: number;
  assessmentTypeCount: number;
  agencies: string[];
  brands: string[];
  regions: string[];
  assessmentTypes: string[];
  agencyTypes?: string[];
  agencyDetails: Array<{
    agencyName: string;
    responseCount: number;
    averageScore: number;
    averageNPS: number;
    brandCount: number;
    regionCount: number;
    surveyCount?: number;
    brands: string[];
    regions: string[];
    agencyTypes?: string[];
  }>;
}

const columns = [
  { id: 'name', label: 'Wave Name' },
  { id: 'status', label: 'Status' },
  { id: 'dateInitiated', label: 'Date Created' },
  { id: 'surveyCount', label: 'Survey Count' },
  { id: 'responseCount', label: 'Response Count' },
  { id: 'view', label: '' },
];

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function descendingComparator(a: any, b: any, orderBy: string) {
  // Special handling for date fields
  if (orderBy === 'dateInitiated') {
    const dateA = new Date(a[orderBy]);
    const dateB = new Date(b[orderBy]);
    if (dateB < dateA) {
      return -1;
    }
    if (dateB > dateA) {
      return 1;
    }
    return 0;
  }

  // Default string/number comparison
  if (b[orderBy] < a[orderBy]) {
    return -1;
  }
  if (b[orderBy] > a[orderBy]) {
    return 1;
  }
  return 0;
}

function getComparator(order: 'asc' | 'desc', orderBy: string) {
  return order === 'desc'
    ? // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (a: any, b: any) => descendingComparator(a, b, orderBy)
    : // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (a: any, b: any) => -descendingComparator(a, b, orderBy);
}

function getStatusColor(
  status: string
):
  | 'default'
  | 'primary'
  | 'secondary'
  | 'error'
  | 'info'
  | 'success'
  | 'warning' {
  switch (status) {
    case 'active':
      return 'success';
    case 'launched':
      return 'info';
    case 'completed':
      return 'primary';
    case 'archived':
      return 'default';
    case 'draft':
      return 'warning';
    case 'seed':
      return 'default';
    default:
      return 'default';
  }
}

export default function WavesTable({ waveVms }: WavesTableProps) {
  const [order, setOrder] = useState<'asc' | 'desc'>('desc');
  const [orderBy, setOrderBy] = useState<string>('dateInitiated');
  const [viewedWaveId, setViewedWaveId] = useState<string | null>(null);
  const [waveStats, setWaveStats] = useState<WaveStats | null>(null);
  const [loadingStats, setLoadingStats] = useState(false);
  // Agency table sorting state
  const [agencyOrder, setAgencyOrder] = useState<'asc' | 'desc'>('desc');
  const [agencyOrderBy, setAgencyOrderBy] = useState<string>('averageScore');
  const [surveyDetailsExpanded, setSurveyDetailsExpanded] = useState(false);
  // Survey details state
  const [surveyDetails, setSurveyDetails] = useState<ISurveyDetail[]>([]);
  const [loadingSurveyDetails, setLoadingSurveyDetails] = useState(false);
  const theme = useTheme();
  const drawerRef = useRef<HTMLTableRowElement>(null);

  const router = useRouter();

  const sortedRows = [...waveVms].sort(getComparator(order, orderBy));

  const handleSort = (property: string) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const handleAgencySort = (property: string) => {
    const isAsc = agencyOrderBy === property && agencyOrder === 'asc';
    setAgencyOrder(isAsc ? 'desc' : 'asc');
    setAgencyOrderBy(property);
  };



  const handleViewWave = async (waveId: string) => {
    setViewedWaveId(waveId);
    setLoadingStats(true);
    setLoadingSurveyDetails(true);
    setWaveStats(null);
    setSurveyDetails([]);
    // Reset agency sorting when viewing a new wave
    setAgencyOrder('desc');
    setAgencyOrderBy('averageScore');
    // Reset accordion state when viewing a new wave
    setSurveyDetailsExpanded(false);

    try {
      // Fetch both stats and survey details in parallel
      const [statsResponse, surveyDetailsResponse] = await Promise.all([
        fetch(`/api/waves/${waveId}/stats`),
        fetch(`/api/waves/${waveId}/survey-details`)
      ]);

      if (statsResponse.ok) {
        const stats = await statsResponse.json();
        setWaveStats(stats);
      } else {
        console.error('Failed to fetch wave stats');
      }

      if (surveyDetailsResponse.ok) {
        const details = await surveyDetailsResponse.json();
        setSurveyDetails(details);
      } else {
        console.error('Failed to fetch survey details');
      }

      // Scroll to the drawer after data is loaded and drawer is expanded
      setTimeout(() => {
        if (drawerRef.current) {
          drawerRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      }, 100);
    } catch (error) {
      console.error('Error fetching wave data:', error);
    } finally {
      setLoadingStats(false);
      setLoadingSurveyDetails(false);
    }
  };

  const handleEditWave = (waveId: string) => {
    router.push(`/admin/launch-wave?edit=${waveId}`);
  };

  const handleSurveyAction = async (surveyId: string, action: SurveyActionType, data?: Record<string, unknown>) => {
    try {
      const response = await fetch(`/api/surveys/${surveyId}/actions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action, data }),
      });

      const result = await response.json();

      if (result.success) {
        // Extract passcode from message if present (handles both new and existing passcodes)
        let passcode: string | null = null;
        if (result.message && result.message.includes('passcode')) {
          const passcodeMatch = result.message.match(/passcode \((\d+)\)/);
          if (passcodeMatch) {
            passcode = passcodeMatch[1];
          }

          // Show success message with passcode information
          alert(result.message);
        }

        // Update the specific survey in the list instead of refetching all
        setSurveyDetails(prevDetails =>
          prevDetails.map(survey => {
            if (survey.id === surveyId) {
              // Update the survey based on the action
              const updatedSurvey = { ...survey };

              if (action === 'remove') {
                updatedSurvey.isRemoved = true;
              } else if (action === 'restore') {
                updatedSurvey.isRemoved = false;
              } else if (action === 'save' && data) {
                // Update survey fields from the edit data
                Object.assign(updatedSurvey, data);

                // If email was changed and we have passcode info, update it
                if (data.userEmail && data.userEmail !== survey.userEmail) {
                  if (passcode) {
                    updatedSurvey.passcode = passcode;
                  } else if (result.message && result.message.includes('Warning')) {
                    // If there was a warning about passcode, we need to refetch to get the correct state
                    // For now, show a placeholder to indicate uncertainty
                    updatedSurvey.passcode = 'Error - refresh needed';
                  }
                }
              }

              return updatedSurvey;
            }
            return survey;
          })
        );

        // You could add a toast notification here
        console.log(result.message);
      } else {
        console.error('Survey action failed:', result.error);
        // You could add error notification here
      }
    } catch (error) {
      console.error('Error performing survey action:', error);
    }
  };

  const handleSendReminders = async (waveId: string) => {
    if (!confirm('Are you sure you want to send reminder emails to all users with incomplete surveys?')) {
      return;
    }

    try {
      const response = await fetch(`/api/waves/${waveId}/send-reminders`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (result.success) {
        alert(result.message);
      } else {
        alert(`Error: ${result.error}`);
      }
    } catch (error) {
      console.error('Error sending reminders:', error);
      alert('Failed to send reminder emails');
    }
  };

  const getCompletionStats = () => {
    if (!surveyDetails.length) {
      return { uniqueEmails: 0, incompleteEmails: 0, completionRate: 0 };
    }

    // Get unique emails (excluding removed surveys)
    const activeEmails = new Set(
      surveyDetails
        .filter(survey => !survey.isRemoved)
        .map(survey => survey.userEmail)
        .filter(email => email && email.trim())
    );

    // Get emails with incomplete surveys
    const incompleteEmails = new Set(
      surveyDetails
        .filter(survey =>
          !survey.isRemoved &&
          survey.surveyStatus !== 'completed'
        )
        .map(survey => survey.userEmail)
        .filter(email => email && email.trim())
    );

    const uniqueEmails = activeEmails.size;
    const incompleteEmailsCount = incompleteEmails.size;
    const completionRate = uniqueEmails > 0 ? ((uniqueEmails - incompleteEmailsCount) / uniqueEmails) * 100 : 0;

    return {
      uniqueEmails,
      incompleteEmails: incompleteEmailsCount,
      completionRate: Math.round(completionRate)
    };
  };

  const handleDownloadCSV = (waveId: string, waveName: string) => {
    // Get the current wave's survey details
    const currentWaveSurveys = surveyDetails;

    if (currentWaveSurveys.length === 0) {
      alert('No survey data available to download');
      return;
    }

    // Create CSV headers
    const headers = [
      'Name',
      'Email',
      'Passcode',
      'Agency',
      'Brand',
      'Country',
      'Region',
      'Survey Status',
      'Progress',
      'Active',
      'Notes'
    ];

    // Convert survey data to CSV rows
    const csvRows = [
      headers.join(','), // Header row
      ...currentWaveSurveys.map(survey => [
        `"${survey.userName || ''}"`,
        `"${survey.userEmail || ''}"`,
        `"${survey.passcode || ''}"`,
        `"${survey.agencyName || ''}"`,
        `"${survey.brand || ''}"`,
        `"${survey.country || ''}"`,
        `"${survey.region || ''}"`,
        `"${formatSurveyStatus(survey.surveyStatus)}"`,
        `"${survey.completionPercentage}%"`,
        `"${survey.isRemoved ? 'No' : 'Yes'}"`,
        `"${survey.notes || ''}"`
      ].join(','))
    ];

    // Create CSV content
    const csvContent = csvRows.join('\n');

    // Create filename with format: WAVENAME-survey-details-YYMMDD.csv
    const today = new Date();
    const dateStr = today.getFullYear().toString().slice(-2) +
                   (today.getMonth() + 1).toString().padStart(2, '0') +
                   today.getDate().toString().padStart(2, '0');
    const filename = `${waveName.replace(/[^a-zA-Z0-9-_]/g, '_')}-survey-details-${dateStr}.csv`;

    // Create and trigger download
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // Reset stats when closing the panel
  useEffect(() => {
    if (!viewedWaveId) {
      setWaveStats(null);
      setSurveyDetails([]);
    }
  }, [viewedWaveId]);

  // WaveDetailsDrawer component
  const WaveDetailsDrawer = ({
    wave,
    waveStats,
    loadingStats,
    surveyDetails,
    loadingSurveyDetails,
    agencyOrder,
    agencyOrderBy,
    onClose,
    onAgencySort,
    onSurveyAction
  }: {
    wave: IWaveVm;
    waveStats: WaveStats | null;
    loadingStats: boolean;
    surveyDetails: ISurveyDetail[];
    loadingSurveyDetails: boolean;
    agencyOrder: 'asc' | 'desc';
    agencyOrderBy: string;
    onClose: () => void;
    onAgencySort: (column: string) => void;
    onSurveyAction: (surveyId: string, action: SurveyActionType, data?: Record<string, unknown>) => Promise<void>;
  }) => {
    const sortedAgencyDetails = waveStats?.agencyDetails ? [...waveStats.agencyDetails].sort(getComparator(agencyOrder, agencyOrderBy)) : [];

    return (
      <DashboardPanel sx={{ mt: 1, mb: 1 }}>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 2,
          }}
        >
          <Typography variant="h6" fontWeight={700}>
            {wave.name}
          </Typography>
          <Button
            variant="outlined"
            size="small"
            onClick={onClose}
          >
            Close
          </Button>
        </Box>

        {loadingStats ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : waveStats ? (
          <>
            {/* Summary Statistics - Only for non-seed waves */}
            {!waveStats.isSeedWave && (
              <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
                <DashboardPanel
                  sx={{ p: 2, textAlign: 'center', flex: 1, minWidth: 150 }}
                >
                  <Typography variant="h4" color="primary" fontWeight={700}>
                    {waveStats.totalResponses}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Responses
                  </Typography>
                </DashboardPanel>
                <DashboardPanel
                  sx={{ p: 2, textAlign: 'center', flex: 1, minWidth: 150 }}
                >
                  <Typography variant="h4" color="primary" fontWeight={700}>
                    {waveStats.completionRate}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Completion Rate
                  </Typography>
                </DashboardPanel>
                <DashboardPanel
                  sx={{ p: 2, textAlign: 'center', flex: 1, minWidth: 150 }}
                >
                  <Typography variant="h4" color="primary" fontWeight={700}>
                    {waveStats.averageScore}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Average Score
                  </Typography>
                </DashboardPanel>
                <DashboardPanel
                  sx={{ p: 2, textAlign: 'center', flex: 1, minWidth: 150 }}
                >
                  <Typography variant="h4" color="primary" fontWeight={700}>
                    {waveStats.averageNPS}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Average NPS
                  </Typography>
                </DashboardPanel>
              </Box>
            )}

            {/* Seed Wave Summary - Only for seed waves */}
            {waveStats.isSeedWave && (
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="h6"
                  fontWeight={600}
                  gutterBottom
                  color="primary"
                >
                  Seed Data Overview
                </Typography>
                <Typography
                  variant="body1"
                  color="text.secondary"
                  sx={{ mb: 2 }}
                >
                  This is seed data containing survey templates for launching
                  new assessment waves. No responses have been collected yet.
                </Typography>
              </Box>
            )}

            {/* Counts Summary */}
            <Box sx={{ display: 'flex', gap: 4, mb: 3, flexWrap: 'wrap' }}>
              <Box>
                <Typography variant="subtitle2" fontWeight={600} gutterBottom>
                  {waveStats.isSeedWave
                    ? 'Available Data'
                    : 'Participating Entities'}
                </Typography>
                <Typography>{waveStats.agencyCount} Agencies</Typography>
                <Typography>{waveStats.brandCount} Brands</Typography>
                <Typography>{waveStats.regionCount} Regions</Typography>
                <Typography>
                  {waveStats.assessmentTypeCount} Assessment Types
                </Typography>
                {waveStats.isSeedWave && waveStats.agencyTypes && (
                  <Typography>
                    {waveStats.agencyTypes.length} Agency Types
                  </Typography>
                )}
              </Box>
              <Box>
                <Typography variant="subtitle2" fontWeight={600} gutterBottom>
                  Wave Info
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    mb: 1,
                  }}
                >
                  <Typography>Status:</Typography>
                  <Chip
                    label={wave.status}
                    size="small"
                    color={getStatusColor(wave.status)}
                  />
                </Box>
                <Typography>
                  Created: {formatDate(wave.dateInitiated)}
                </Typography>
                <Typography>
                  Total Surveys: {waveStats.totalSurveys}
                </Typography>
              </Box>

              {/* Survey Completion Stats - Only show for launched waves with survey details loaded */}
              {wave.status === 'launched' && surveyDetails.length > 0 && (() => {
                const stats = getCompletionStats();
                return (
                  <Box>
                    <Typography variant="subtitle2" fontWeight={600} gutterBottom>
                      Survey Completion
                    </Typography>
                    <Typography>
                      Recipients: {stats.uniqueEmails}
                    </Typography>
                    <Typography>
                      Need Reminders: {stats.incompleteEmails}
                    </Typography>
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        mb: 1,
                      }}
                    >
                      <Typography>Completion:</Typography>
                      <Chip
                        label={`${stats.completionRate}%`}
                        size="small"
                        color={stats.completionRate >= 80 ? 'success' : stats.completionRate >= 50 ? 'warning' : 'error'}
                      />
                    </Box>
                    <Button
                      variant="contained"
                      startIcon={<EmailIcon />}
                      onClick={() => handleSendReminders(viewedWaveId!)}
                      disabled={loadingSurveyDetails || stats.incompleteEmails === 0}
                      size="small"
                      color="primary"
                      sx={{ mt: 1 }}
                    >
                      Send Reminders ({stats.incompleteEmails})
                    </Button>
                  </Box>
                );
              })()}
            </Box>

            {/* Agency Performance Details Accordion - Only for non-seed waves */}
            {!waveStats.isSeedWave && (
              <Accordion>
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  aria-controls="agency-details-content"
                  id="agency-details-header"
                >
                  <Typography variant="h6" fontWeight={600}>
                    Agency Performance Details (
                    {waveStats.agencyDetails.length} agencies)
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <TableContainer>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell sx={{ fontWeight: 700 }}>
                            <TableSortLabel
                              active={agencyOrderBy === 'agencyName'}
                              direction={
                                agencyOrderBy === 'agencyName'
                                  ? agencyOrder
                                  : 'asc'
                              }
                              onClick={() => onAgencySort('agencyName')}
                            >
                              Agency
                            </TableSortLabel>
                          </TableCell>
                          <TableCell sx={{ fontWeight: 700 }}>
                            <TableSortLabel
                              active={agencyOrderBy === 'responseCount'}
                              direction={
                                agencyOrderBy === 'responseCount'
                                  ? agencyOrder
                                  : 'asc'
                              }
                              onClick={() =>
                                onAgencySort('responseCount')
                              }
                            >
                              Responses
                            </TableSortLabel>
                          </TableCell>
                          <TableCell sx={{ fontWeight: 700 }}>
                            <TableSortLabel
                              active={agencyOrderBy === 'averageScore'}
                              direction={
                                agencyOrderBy === 'averageScore'
                                  ? agencyOrder
                                  : 'asc'
                              }
                              onClick={() => onAgencySort('averageScore')}
                            >
                              Avg Score
                            </TableSortLabel>
                          </TableCell>
                          <TableCell sx={{ fontWeight: 700 }}>
                            <TableSortLabel
                              active={agencyOrderBy === 'averageNPS'}
                              direction={
                                agencyOrderBy === 'averageNPS'
                                  ? agencyOrder
                                  : 'asc'
                              }
                              onClick={() => onAgencySort('averageNPS')}
                            >
                              NPS
                            </TableSortLabel>
                          </TableCell>
                          <TableCell sx={{ fontWeight: 700 }}>
                            <TableSortLabel
                              active={agencyOrderBy === 'brandCount'}
                              direction={
                                agencyOrderBy === 'brandCount'
                                  ? agencyOrder
                                  : 'asc'
                              }
                              onClick={() => onAgencySort('brandCount')}
                            >
                              Brands
                            </TableSortLabel>
                          </TableCell>
                          <TableCell sx={{ fontWeight: 700 }}>
                            <TableSortLabel
                              active={agencyOrderBy === 'regionCount'}
                              direction={
                                agencyOrderBy === 'regionCount'
                                  ? agencyOrder
                                  : 'asc'
                              }
                              onClick={() => onAgencySort('regionCount')}
                            >
                              Regions
                            </TableSortLabel>
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {sortedAgencyDetails.map((agency, idx) => (
                          <TableRow
                            key={agency.agencyName}
                            sx={{
                              backgroundColor:
                                idx % 2 === 0
                                  ? theme.palette.background.paper
                                  : theme.palette.action.hover,
                            }}
                          >
                            <TableCell>{agency.agencyName}</TableCell>
                            <TableCell>{agency.responseCount}</TableCell>
                            <TableCell>{agency.averageScore}</TableCell>
                            <TableCell>{agency.averageNPS}</TableCell>
                            <TableCell>{agency.brandCount}</TableCell>
                            <TableCell>{agency.regionCount}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </AccordionDetails>
              </Accordion>
            )}

            {/* Agency Details for Seed Waves - Shows survey counts instead of response data */}
            {waveStats.isSeedWave && (
              <Accordion>
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  aria-controls="agency-seed-details-content"
                  id="agency-seed-details-header"
                >
                  <Typography variant="h6" fontWeight={600}>
                    Agency Seed Data Details ({waveStats.agencyDetails.length}{' '}
                    agencies)
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <TableContainer>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell sx={{ fontWeight: 700 }}>
                            <TableSortLabel
                              active={agencyOrderBy === 'agencyName'}
                              direction={
                                agencyOrderBy === 'agencyName'
                                  ? agencyOrder
                                  : 'asc'
                              }
                              onClick={() => onAgencySort('agencyName')}
                            >
                              Agency
                            </TableSortLabel>
                          </TableCell>
                          <TableCell sx={{ fontWeight: 700 }}>
                            <TableSortLabel
                              active={agencyOrderBy === 'surveyCount'}
                              direction={
                                agencyOrderBy === 'surveyCount'
                                  ? agencyOrder
                                  : 'asc'
                              }
                              onClick={() => onAgencySort('surveyCount')}
                            >
                              Surveys
                            </TableSortLabel>
                          </TableCell>
                          <TableCell sx={{ fontWeight: 700 }}>
                            <TableSortLabel
                              active={agencyOrderBy === 'brandCount'}
                              direction={
                                agencyOrderBy === 'brandCount'
                                  ? agencyOrder
                                  : 'asc'
                              }
                              onClick={() => onAgencySort('brandCount')}
                            >
                              Brands
                            </TableSortLabel>
                          </TableCell>
                          <TableCell sx={{ fontWeight: 700 }}>
                            <TableSortLabel
                              active={agencyOrderBy === 'regionCount'}
                              direction={
                                agencyOrderBy === 'regionCount'
                                  ? agencyOrder
                                  : 'asc'
                              }
                              onClick={() => onAgencySort('regionCount')}
                            >
                              Regions
                            </TableSortLabel>
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {sortedAgencyDetails.map((agency, idx) => (
                          <TableRow
                            key={agency.agencyName}
                            sx={{
                              backgroundColor:
                                idx % 2 === 0
                                  ? theme.palette.background.paper
                                  : theme.palette.action.hover,
                            }}
                          >
                            <TableCell>{agency.agencyName}</TableCell>
                            <TableCell>{agency.surveyCount || 0}</TableCell>
                            <TableCell>{agency.brandCount}</TableCell>
                            <TableCell>{agency.regionCount}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </AccordionDetails>
              </Accordion>
            )}

            {/* Lists Accordion */}
            <Accordion>
              <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                aria-controls="lists-content"
                id="lists-header"
              >
                <Typography variant="h6" fontWeight={600}>
                  Complete Lists (
                  {waveStats.isSeedWave
                    ? 'Available Data'
                    : 'Participating Entities'}
                  )
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap' }}>
                  <Box sx={{ flex: 1, minWidth: 200 }}>
                    <Typography
                      variant="subtitle2"
                      fontWeight={600}
                      gutterBottom
                    >
                      Agencies ({waveStats.agencies.length})
                    </Typography>
                    <Box sx={{ maxHeight: 200, overflow: 'auto' }}>
                      {waveStats.agencies.map((agency) => (
                        <Typography
                          key={agency}
                          variant="body2"
                          sx={{ py: 0.5 }}
                        >
                          • {agency}
                        </Typography>
                      ))}
                    </Box>
                  </Box>
                  <Box sx={{ flex: 1, minWidth: 200 }}>
                    <Typography
                      variant="subtitle2"
                      fontWeight={600}
                      gutterBottom
                    >
                      Brands ({waveStats.brands.length})
                    </Typography>
                    <Box sx={{ maxHeight: 200, overflow: 'auto' }}>
                      {waveStats.brands.map((brand) => (
                        <Typography
                          key={brand}
                          variant="body2"
                          sx={{ py: 0.5 }}
                        >
                          • {brand}
                        </Typography>
                      ))}
                    </Box>
                  </Box>
                  <Box sx={{ flex: 1, minWidth: 200 }}>
                    <Typography
                      variant="subtitle2"
                      fontWeight={600}
                      gutterBottom
                    >
                      Regions ({waveStats.regions.length})
                    </Typography>
                    <Box sx={{ maxHeight: 200, overflow: 'auto' }}>
                      {waveStats.regions.map((region) => (
                        <Typography
                          key={region}
                          variant="body2"
                          sx={{ py: 0.5 }}
                        >
                          • {region}
                        </Typography>
                      ))}
                    </Box>
                  </Box>
                  {waveStats.isSeedWave && waveStats.agencyTypes && (
                    <Box sx={{ flex: 1, minWidth: 200 }}>
                      <Typography
                        variant="subtitle2"
                        fontWeight={600}
                        gutterBottom
                      >
                        Agency Types ({waveStats.agencyTypes.length})
                      </Typography>
                      <Box sx={{ maxHeight: 200, overflow: 'auto' }}>
                        {waveStats.agencyTypes.map((agencyType) => (
                          <Typography
                            key={agencyType}
                            variant="body2"
                            sx={{ py: 0.5 }}
                          >
                            • {agencyType}
                          </Typography>
                        ))}
                      </Box>
                    </Box>
                  )}
                </Box>
              </AccordionDetails>
            </Accordion>

            {/* Survey Details Accordion - Only for non-seed waves */}
            {!waveStats.isSeedWave && (
              <Accordion
                expanded={surveyDetailsExpanded}
                onChange={(_, isExpanded) => setSurveyDetailsExpanded(isExpanded)}
              >
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  aria-controls="survey-details-content"
                  id="survey-details-header"
                >
                  <Typography variant="h6" fontWeight={600}>
                    Survey Details ({surveyDetails.length} surveys)
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Stack direction="row" justifyContent="flex-end" sx={{ mb: 2 }}>
                    <Button
                      variant="outlined"
                      startIcon={<DownloadIcon />}
                      onClick={() => handleDownloadCSV(viewedWaveId!, wave.name)}
                      disabled={loadingSurveyDetails || surveyDetails.length === 0}
                      size="small"
                    >
                      Download CSV
                    </Button>
                  </Stack>
                  <SurveyDetailsTable
                    surveys={surveyDetails}
                    loading={loadingSurveyDetails}
                    onAction={onSurveyAction}
                  />
                </AccordionDetails>
              </Accordion>
            )}
          </>
        ) : (
          <Typography
            color="text.secondary"
            sx={{ p: 2, textAlign: 'center' }}
          >
            Failed to load wave statistics
          </Typography>
        )}
      </DashboardPanel>
    );
  };

  return (
    <>
      <TableContainer sx={{ width: '100%', overflowX: 'hidden' }}>
        <Table
          size="small"
          stickyHeader
          sx={{
            borderTop: `1px solid ${theme.palette.divider}`,
            width: '100%',
            tableLayout: 'fixed',
            '& .MuiTableCell-root': {
              wordWrap: 'break-word',
              whiteSpace: 'normal',
              overflow: 'hidden',
            },
          }}
        >
          <TableHead>
            <TableRow
              sx={{
                backgroundColor: theme.palette.action.hover,
                '& .MuiTableCell-head': {
                  fontWeight: 600,
                  backgroundColor: theme.palette.action.hover,
                },
              }}
            >
              {columns.map((col) => (
                <TableCell key={col.id} sx={{ fontWeight: 700 }}>
                  {col.id !== 'view' ? (
                    <TableSortLabel
                      active={orderBy === col.id}
                      direction={orderBy === col.id ? order : 'asc'}
                      onClick={() => handleSort(col.id)}
                    >
                      {col.label}
                    </TableSortLabel>
                  ) : (
                    col.label
                  )}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {sortedRows.map((wave, index) => [
              <TableRow
                key={`${wave.id}-main`}
                sx={{
                  backgroundColor:
                    index % 2 === 0
                      ? theme.palette.background.paper
                      : theme.palette.action.hover,
                  '&:hover': {
                    backgroundColor: theme.palette.action.selected,
                  },
                }}
              >
                <TableCell>{wave.name}</TableCell>
                <TableCell>
                  <Chip
                    label={wave.status}
                    size="small"
                    color={getStatusColor(wave.status)}
                  />
                </TableCell>
                <TableCell>{formatDate(wave.dateInitiated)}</TableCell>
                <TableCell>{wave.surveyCount || 0}</TableCell>
                <TableCell>{wave.responseCount || 0}</TableCell>
                <TableCell>
                  {wave.status === 'draft' ? (
                    <Stack direction="row" spacing={1}>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() => handleEditWave(wave.id)}
                      >
                        Edit
                      </Button>
                      <Button
                        variant={
                          viewedWaveId === wave.id ? 'contained' : 'outlined'
                        }
                        size="small"
                        onClick={() => handleViewWave(wave.id)}
                        disabled={viewedWaveId === wave.id}
                      >
                        View
                      </Button>
                    </Stack>
                  ) : (
                    <Button
                      variant={
                        viewedWaveId === wave.id ? 'contained' : 'outlined'
                      }
                      size="small"
                      onClick={() => handleViewWave(wave.id)}
                      disabled={viewedWaveId === wave.id}
                    >
                      View
                    </Button>
                  )}
                </TableCell>
              </TableRow>,
              /* Drawer row for wave details */
              <TableRow
                key={`${wave.id}-drawer`}
                ref={viewedWaveId === wave.id ? drawerRef : null}
                sx={{
                  '& > *': {
                    borderBottom: 'unset',
                  },
                }}
              >
                <TableCell
                  style={{ paddingBottom: 0, paddingTop: 0 }}
                  colSpan={6}
                >
                  <Collapse
                    in={viewedWaveId === wave.id}
                    timeout="auto"
                    unmountOnExit
                  >
                    <Box sx={{ margin: 1 }}>
                      {viewedWaveId === wave.id && (
                        <WaveDetailsDrawer
                          wave={wave}
                          waveStats={waveStats}
                          loadingStats={loadingStats}
                          surveyDetails={surveyDetails}
                          loadingSurveyDetails={loadingSurveyDetails}
                          agencyOrder={agencyOrder}
                          agencyOrderBy={agencyOrderBy}
                          onClose={() => setViewedWaveId(null)}
                          onAgencySort={handleAgencySort}
                          onSurveyAction={handleSurveyAction}
                        />
                      )}
                    </Box>
                  </Collapse>
                </TableCell>
              </TableRow>
            ])}
          </TableBody>
        </Table>
      </TableContainer>
    </>
  );
}

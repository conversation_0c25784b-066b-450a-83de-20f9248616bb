'use client';

import {
  Box,
  Typography,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Autocomplete,
  TextField,
  OutlinedInput,
  Checkbox,
  ListItemText,
  SelectChangeEvent,
} from '@mui/material';
import { useState, useEffect } from 'react';
import DataTableTable, { DataTableColumn } from './DataTableTable';
import { ElevatedFormControl } from '@/app/_components/ElevatedFormControl';

interface AnalyticsData {
  agencyName: string;
  agencyType: string;
  averageScore: number;
  responseCount: number;
  npsScore: number;
  q1Avg: number;
  q2Avg: number;
  q3Avg: number;
  q4Avg: number;
  q5Avg: number;
  brandCount: number;
  regionCount: number;
}

interface WaveOption {
  label: string;  // Display label with date prefix
  value: string;  // Original wave name for filtering
}

interface FilterOptions {
  agencies: string[];
  brands: string[];
  regions: string[];
  waves: WaveOption[];
}

const columns = [
  { id: 'agencyName', label: 'AGENCY' },
  { id: 'q1Avg', label: 'PEOPLE & PARTNERSHIP' },
  { id: 'q2Avg', label: 'ACCOUNT MGMT & PROCESS' },
  { id: 'q3Avg', label: 'STRATEGY' },
  { id: 'q4Avg', label: 'CREATIVITY' },
  { id: 'q5Avg', label: 'MEDIA PLANNING & EXECUTION' },
  { id: 'averageScore', label: 'AVG' },
  { id: 'npsScore', label: 'NPS' },
  { id: 'responseCount', label: 'RESPONSES' },
];

type Order = 'asc' | 'desc';

type DataRow = {
  agencyName: string;
  q1Avg: number;
  q2Avg: number;
  q3Avg: number;
  q4Avg: number;
  q5Avg: number;
  averageScore: number;
  npsScore: number;
  responseCount: number;
};

type ColumnId = keyof DataRow;

// Custom render function for wave selection to prevent horizontal overflow
const renderWaveValue = (selected: string[], filterOptions: FilterOptions) => {
  if (selected.length === 0) return '';

  // Map selected values to their display labels
  const selectedLabels = selected.map(value => {
    const waveOption = filterOptions.waves.find(w => w.value === value);
    return waveOption ? waveOption.label : value;
  });

  if (selectedLabels.length === 1) return selectedLabels[0];
  if (selectedLabels.length <= 3) return selectedLabels.join(', ');
  return `${selectedLabels.slice(0, 2).join(', ')} +${selectedLabels.length - 2} more`;
};

export default function DataTableClient() {
  const [selectedAgency, setSelectedAgency] = useState('All Agencies');
  const [selectedBrand, setSelectedBrand] = useState('All Brands');
  const [selectedRegion, setSelectedRegion] = useState('All Regions');
  const [selectedWaves, setSelectedWaves] = useState<string[]>([]);
  const [tempSelectedWaves, setTempSelectedWaves] = useState<string[]>([]);
  const [orderBy, setOrderBy] = useState<ColumnId>('averageScore');
  const [order, setOrder] = useState<Order>('desc');
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    agencies: ['All Agencies'],
    brands: ['All Brands'],
    regions: ['All Regions'],
    waves: [],
  });

  // Fetch filter options on component mount
  useEffect(() => {
    const fetchFilterOptions = async () => {
      try {
        const [
          brandsResponse,
          regionsResponse,
          wavesResponse,
          agenciesResponse,
        ] = await Promise.all([
          fetch('/api/surveys/unique-brands'),
          fetch('/api/surveys/unique-regions'),
          fetch('/api/waves?excludeSeed=true'),
          fetch('/api/surveys/unique-agencies'),
        ]);

        const [brands, regions, waves, agencies] = await Promise.all([
          brandsResponse.json(),
          regionsResponse.json(),
          wavesResponse.json(),
          agenciesResponse.json(),
        ]);

        const waveOptions = waves.map((w: { name: string; displayLabel?: string; responseCount?: number }) => ({
          label: `${w.displayLabel || w.name} (${w.responseCount || 0} responses)`,
          value: w.name,
        }));

        setFilterOptions({
          agencies: ['All Agencies', ...agencies],
          brands: ['All Brands', ...brands],
          regions: ['All Regions', ...regions],
          waves: waveOptions,
        });

        // Set the latest wave as default selection
        if (waveOptions.length > 0) {
          const latestWave = waveOptions[0].value; // First item is newest since API sorts by createdAt: -1
          setSelectedWaves([latestWave]);
          setTempSelectedWaves([latestWave]); // Initialize temp state
        }
      } catch (error) {
        console.error('Error fetching filter options:', error);
      }
    };

    fetchFilterOptions();
  }, []);

  // Fetch analytics data when filters change
  useEffect(() => {
    const fetchAnalyticsData = async () => {
      try {
        setLoading(true);
        setError(null);

        const params = new URLSearchParams();
        if (selectedAgency !== 'All Agencies')
          params.append('agencyName', selectedAgency);
        if (selectedBrand !== 'All Brands')
          params.append('brand', selectedBrand);
        if (selectedRegion !== 'All Regions')
          params.append('region', selectedRegion);
        if (selectedWaves.length > 0) params.append('wave', selectedWaves.join(','));

        const response = await fetch(
          `/api/responses/detailed-analytics?${params.toString()}`
        );

        if (!response.ok) {
          throw new Error('Failed to fetch analytics data');
        }

        const data = await response.json();
        setAnalyticsData(data);
      } catch (error) {
        console.error('Error fetching analytics data:', error);
        setError(error instanceof Error ? error.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchAnalyticsData();
  }, [selectedAgency, selectedBrand, selectedRegion, selectedWaves]);

  // Transform analytics data to match table structure
  const transformedData: DataRow[] = analyticsData.map((item) => ({
    agencyName: item.agencyName,
    q1Avg: item.q1Avg,
    q2Avg: item.q2Avg,
    q3Avg: item.q3Avg,
    q4Avg: item.q4Avg,
    q5Avg: item.q5Avg,
    averageScore: item.averageScore,
    npsScore: item.npsScore,
    responseCount: item.responseCount,
  }));

  // Since we're now aggregating at the API level, no additional filtering needed
  const sorted = [...transformedData].sort((a, b) => {
    if (orderBy === 'agencyName') {
      return order === 'asc'
        ? a.agencyName.localeCompare(b.agencyName)
        : b.agencyName.localeCompare(a.agencyName);
    } else {
      return order === 'asc'
        ? (a[orderBy] as number) - (b[orderBy] as number)
        : (b[orderBy] as number) - (a[orderBy] as number);
    }
  });

  const handleSort = (property: string) => {
    setOrderBy(property as ColumnId);
    setOrder(orderBy === property && order === 'asc' ? 'desc' : 'asc');
  };

  const handleWaveChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    setTempSelectedWaves(typeof value === 'string' ? value.split(',') : value);
  };

  const handleWaveClose = () => {
    setSelectedWaves(tempSelectedWaves);
  };

  if (error) {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, mt: 1 }}>
        <Typography color="error">Error: {error}</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
      <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
        <ElevatedFormControl size="small" sx={{ width: '100%', maxWidth: 300 }}>
          <InputLabel>Wave</InputLabel>
          <Select
            multiple
            value={tempSelectedWaves}
            onChange={handleWaveChange}
            onClose={handleWaveClose}
            input={<OutlinedInput label="Wave" />}
            renderValue={(selected) => renderWaveValue(selected, filterOptions)}
          >
            {filterOptions.waves.map((wave) => (
              <MenuItem key={wave.value} value={wave.value}>
                <Checkbox checked={tempSelectedWaves.indexOf(wave.value) > -1} />
                <ListItemText primary={wave.label} />
              </MenuItem>
            ))}
          </Select>
        </ElevatedFormControl>
        <ElevatedFormControl size="small">
          <Autocomplete
            options={filterOptions.agencies}
            value={selectedAgency}
            onChange={(event: React.SyntheticEvent, value: string | null) =>
              setSelectedAgency(value || 'All Agencies')
            }
            renderInput={(params) => (
              <TextField {...params} label="Agencies" size="small" />
            )}
            size="small"
            sx={{ minWidth: 140 }}
            disableClearable
          />
        </ElevatedFormControl>
        <ElevatedFormControl size="small">
          <Autocomplete
            options={filterOptions.brands}
            value={selectedBrand}
            onChange={(event: React.SyntheticEvent, value: string | null) =>
              setSelectedBrand(value || 'All Brands')
            }
            renderInput={(params) => (
              <TextField {...params} label="Brands" size="small" />
            )}
            size="small"
            sx={{ minWidth: 140 }}
            disableClearable
          />
        </ElevatedFormControl>
        <ElevatedFormControl size="small">
          <Autocomplete
            options={filterOptions.regions}
            value={selectedRegion}
            onChange={(event: React.SyntheticEvent, value: string | null) =>
              setSelectedRegion(value || 'All Regions')
            }
            renderInput={(params) => (
              <TextField {...params} label="Regions" size="small" />
            )}
            size="small"
            sx={{ minWidth: 140 }}
            disableClearable
          />
        </ElevatedFormControl>
      </Box>

      {/* Show message if no waves selected */}
      {selectedWaves.length === 0 && (
        <Box
          sx={{
            mt: 4,
            p: 3,
            textAlign: 'center',
            bgcolor: 'grey.100',
            borderRadius: 1,
          }}
        >
          <Typography variant="h6" color="text.secondary">
            No Wave Selected
          </Typography>
          <Typography color="text.secondary">
            Please select at least one wave to view data table results.
          </Typography>
        </Box>
      )}

      {/* Main content - only show if waves are selected */}
      {selectedWaves.length > 0 && (
        <>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <DataTableTable
              rows={sorted}
              columns={columns as DataTableColumn[]}
              order={order}
              orderBy={orderBy}
              handleSort={handleSort}
            />
          )}
        </>
      )}
    </Box>
  );
}

'use client';

import { Typography, Box, CircularProgress, useTheme } from '@mui/material';
import { LineChart } from '@mui/x-charts/LineChart';
import { useState, useEffect } from 'react';
import PanelHeader from './PanelHeader';
import PanelSubheader from './PanelSubheader';
import DashboardPanel from './DashboardPanel';

interface ABIOnAgencyWaveHistoryPanelProps {
	focusType: 'agency' | 'brand' | 'region' | 'wave';
	focusValue: string;
}

interface ChartDataPoint {
	wave: string;
	score: number;
	responseCount: number;
	[key: string]: string | number | undefined;
}

interface WaveHistoryData {
	waveNames: string[];
	abiOnAgency: {
		scores: (number | null)[];
		npsScores: (number | null)[];
		responseCounts: number[];
	};
	agencyOnAbi: {
		scores: (number | null)[];
		npsScores: (number | null)[];
		responseCounts: number[];
	};
	responseCount: number;
}

export default function ABIOnAgencyWaveHistoryPanel({
	focusType,
	focusValue,
}: ABIOnAgencyWaveHistoryPanelProps) {
	const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	const theme = useTheme();

	useEffect(() => {
		const fetchWaveHistory = async () => {
			try {
				setLoading(true);
				setError(null);

				const params = new URLSearchParams({
					focusType,
					focusValue,
				});

				const response = await fetch(
					`/api/responses/wave-history?${params.toString()}`
				);

				if (!response.ok) {
					throw new Error('Failed to fetch wave history');
				}

				const data: WaveHistoryData = await response.json();
				
				// Transform ABI-on-Agency data for MUI X Charts
				const transformedData: ChartDataPoint[] = data.waveNames.map(
					(wave: string, index: number) => ({
						wave,
						score: data.abiOnAgency.scores[index] || 0,
						responseCount: data.abiOnAgency.responseCounts[index] || 0,
					})
				).filter(point => point.score > 0); // Only include waves with ABI-on-Agency data

				setChartData(transformedData);
			} catch (err) {
				console.error('Error fetching ABI-on-Agency wave history:', err);
				setError('Failed to load wave history');
				setChartData([]);
			} finally {
				setLoading(false);
			}
		};

		fetchWaveHistory();
	}, [focusType, focusValue]);

	// If no data, show a message
	if (!loading && chartData.length === 0) {
		return (
			<DashboardPanel sx={{ p: 2, bgcolor: '#e3f2fd', height: 300 }}>
				<PanelHeader gutterBottom>ABI → Agency</PanelHeader>
				<PanelSubheader gutterBottom sx={{ mb: 1 }}>
					{focusValue} Wave History
				</PanelSubheader>
				<Box
					sx={{
						display: 'flex',
						flexDirection: 'column',
						alignItems: 'center',
						justifyContent: 'center',
						height: 200,
					}}
				>
					<Typography variant="body2" color="text.secondary" textAlign="center">
						No ABI → Agency wave history data available for {focusValue}.
					</Typography>
				</Box>
			</DashboardPanel>
		);
	}

	return (
		<DashboardPanel sx={{ p: 2, textAlign: 'center' }}>
			<PanelHeader>ABI → Agency</PanelHeader>
			<PanelSubheader gutterBottom sx={{ mb: 1 }}>
				{focusValue} Wave History
			</PanelSubheader>

			{loading ? (
				<Box
					sx={{
						display: 'flex',
						alignItems: 'center',
						justifyContent: 'center',
						height: 200,
					}}
				>
					<CircularProgress size={24} />
				</Box>
			) : (
				<Box
					sx={{
						display: 'flex',
						flexDirection: 'column',
						alignItems: 'center',
						gap: 2,
						textAlign: 'center',
					}}
				>
					{error && (
						<Typography variant="body2" color="error" sx={{ mb: 1 }}>
							{error}
						</Typography>
					)}

					{/* MUI X Charts LineChart */}
					<Box
						sx={{
							width: '100%',
							position: 'relative',
							textAlign: 'center',
						}}
					>
						<LineChart
							dataset={chartData}
							xAxis={[
								{
									scaleType: 'point',
									dataKey: 'wave',
									tickLabelStyle: {
										display: 'none',
										fontSize: 10,
										fill: theme.palette.text.secondary,
									},
								},
							]}
							yAxis={[
								{
									min: 1,
									max: 5,
									tickNumber: 5,
									tickLabelStyle: {
										display: 'none',
										fontSize: 10,
										fill: theme.palette.text.secondary,
									},
								},
							]}
							series={[
								{
									dataKey: 'score',
									label: 'ABI → Agency Score',
									color: theme.palette.primary.main,
									curve: 'natural' as const,
									showMark: true,
									area: false,
									valueFormatter: (value: number | null, context) => {
										if (value === null) return '';
										const dataIndex = context?.dataIndex;
										if (typeof dataIndex === 'number' && chartData[dataIndex]) {
											const responseCount = chartData[dataIndex].responseCount;
											return `${value} (${responseCount} responses)`;
										}
										return value.toString();
									},
								},
							]}
							height={240}
							margin={{ left: 30, right: 20, top: 30, bottom: 30 }}
							slots={{
								mark: ({ x, y, dataIndex }) => {
									if (
										typeof x !== 'number' ||
										typeof y !== 'number' ||
										typeof dataIndex !== 'number'
									) {
										return null;
									}
									const markerColor = theme.palette.primary.main;
									const value = chartData[dataIndex]?.score;

									return (
										<g key={`${markerColor}-${dataIndex}`}>
											<circle
												cx={x}
												cy={y}
												r={4}
												fill={`${markerColor}80`}
												stroke={markerColor}
												strokeWidth={2}
												style={{
													filter: 'drop-shadow(0px 2px 4px rgba(0,0,0,0.2))',
												}}
											/>
											<text
												x={x}
												y={y - 12}
												textAnchor="middle"
												fontSize={11}
												fontWeight="bold"
												fill={markerColor}
											>
												{value}
											</text>
										</g>
									);
								},
							}}
							sx={{
								border: '1px solid rgba(0, 0, 0, 0.05)',
								borderRadius: 2,
								'& .MuiChartsAxis-tick': {
									stroke: 'rgba(0, 0, 0, 0.1)',
								},
								'& .MuiChartsAxis-line': {
									stroke: 'rgba(0, 0, 0, 0.1)',
								},
								'& .MuiLineElement-root': {
									strokeWidth: 3,
								},
							}}
						/>
					</Box>

					{/* Current trend indicator */}
					{chartData.length > 1 && (
						<Box
							sx={{
								textAlign: 'center',
								borderRadius: 1,
							}}
						>
							<Box
								sx={{
									display: 'flex',
									justifyContent: 'center',
									gap: 3,
									mb: 1,
								}}
							>
								<Typography
									variant="body2"
									sx={{
										color: (() => {
											const currentScore = chartData[chartData.length - 1]?.score || 0;

											// Calculate rolling 3-wave average as reference point
											let referenceAverage: number;
											if (chartData.length >= 4) {
												const referenceWaves = chartData.slice(-4, -1);
												referenceAverage = referenceWaves.reduce((sum, wave) => sum + (wave.score || 0), 0) / referenceWaves.length;
											} else if (chartData.length >= 2) {
												const referenceWaves = chartData.slice(0, -1);
												referenceAverage = referenceWaves.reduce((sum, wave) => sum + (wave.score || 0), 0) / referenceWaves.length;
											} else {
												return theme.palette.text.secondary;
											}

											const difference = currentScore - referenceAverage;
											if (Math.abs(difference) <= 0.1) {
												return theme.palette.text.secondary;
											} else if (difference > 0.1) {
												return theme.palette.success.main;
											} else {
												return theme.palette.error.main;
											}
										})(),
									}}
								>
									{(() => {
										const currentScore = chartData[chartData.length - 1]?.score || 0;

										// Calculate rolling 3-wave average as reference point
										let referenceAverage: number;
										if (chartData.length >= 4) {
											const referenceWaves = chartData.slice(-4, -1);
											referenceAverage = referenceWaves.reduce((sum, wave) => sum + (wave.score || 0), 0) / referenceWaves.length;
										} else if (chartData.length >= 2) {
											const referenceWaves = chartData.slice(0, -1);
											referenceAverage = referenceWaves.reduce((sum, wave) => sum + (wave.score || 0), 0) / referenceWaves.length;
										} else {
											return '→ Stable';
										}

										const difference = currentScore - referenceAverage;
										if (Math.abs(difference) <= 0.1) {
											return '→ Stable';
										} else if (difference > 0.1) {
											return '↗ Improving';
										} else {
											return '↘ Declining';
										}
									})()}
								</Typography>
							</Box>
						</Box>
					)}
				</Box>
			)}
		</DashboardPanel>
	);
}

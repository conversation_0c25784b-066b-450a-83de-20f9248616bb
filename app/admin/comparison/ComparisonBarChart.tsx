'use client';

import { Bar<PERSON><PERSON> } from '@mui/x-charts/BarChart';
import { Box, CircularProgress, Typography } from '@mui/material';
import { useState, useEffect } from 'react';

interface ComparisonBarChartProps {
  focus: 'brand' | 'wave' | 'agency' | 'agencyType';
  itemsToCompare: string[];
  agency: string;
  brand: string;
  region: string;
  wave: string;
  respondentType: string;
  height?: number;
}

interface ComparisonData {
  categories: string[];
  brands: {
    brand: string;
    data: number[];
    responseCount: number;
  }[];
}

export default function ComparisonBarChart({
  focus,
  itemsToCompare,
  agency,
  brand,
  region,
  wave,
  respondentType,
  height = 420,
}: ComparisonBarChartProps) {
  const [data, setData] = useState<ComparisonData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchComparisonData = async () => {
      if (itemsToCompare.length === 0 || !wave) {
        setData(null);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const params = new URLSearchParams({
          brands: itemsToCompare.join(','),
          focus,
          wave,
          respondentType,
        });

        if (agency !== 'All Agencies') {
          params.append('agencyName', agency);
        }
        if (brand !== 'All Brands') {
          params.append('brand', brand);
        }
        if (region !== 'All Regions') {
          params.append('region', region);
        }

        const response = await fetch(
          `/api/responses/comparison?${params.toString()}`
        );

        if (!response.ok) {
          throw new Error('Failed to fetch comparison data');
        }

        const result = await response.json();
        setData(result);
      } catch (err) {
        console.error('Error fetching comparison data:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchComparisonData();
  }, [itemsToCompare, agency, brand, region, wave, respondentType, focus]);

  if (loading) {
    return (
      <Box
        sx={{
          width: '100%',
          height,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box
        sx={{
          width: '100%',
          height,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Typography color="error" variant="body2">
          Error loading chart: {error}
        </Typography>
      </Box>
    );
  }

  if (!data || data.brands.length === 0) {
    return (
      <Box
        sx={{
          width: '100%',
          height,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Typography color="text.secondary" variant="body2">
          No data available for the selected filters
        </Typography>
      </Box>
    );
  }

  // Transform data for BarChart dataset format
  const chartDataset = data.categories.map((category, idx) => {
    const dataPoint: Record<string, string | number> = { category };
    data.brands.forEach((brandData) => {
      dataPoint[brandData.brand] = brandData.data[idx] || 0;
    });
    return dataPoint;
  });

  const series = data.brands.map((brandData, idx) => ({
    dataKey: brandData.brand,
    label: brandData.brand,
    color:
      idx === 0
        ? '#1976d2'
        : idx === 1
        ? '#d32f2f'
        : idx === 2
        ? '#2e7d32'
        : idx === 3
        ? '#ed6c02'
        : '#9c27b0',
    id: idx,
  }));

  return (
    <Box
      sx={{
        width: '100%',
        // height: height + 280,
        flexGrow: 1,
        minHeight: 0,
        padding: 1,
        position: 'relative',
        zIndex: 1,
      }}
    >
      <BarChart
        dataset={chartDataset}
        xAxis={[
          {
            tickLabelInterval: () => true,
            scaleType: 'band',
            dataKey: 'category',
            tickLabelStyle: {
              // textAnchor: 'start',
            },
          },
        ]}
        yAxis={[
          {
            stroke: '#e0e0e0',
            min: 0,
            max: 5,
            tickNumber: 6,
            tickLabelInterval: () => true,
            tickLabelStyle: {
              // fontSize: 12,
            },
          },
        ]}
        series={series}
        barLabel="value"
        height={height - 40}
        margin={{ left: 0, right: 30, top: 20, bottom: 60 }}
        sx={{
          border: '1px solid rgba(0, 0, 0, 0.05)',
          borderRadius: 2,
          '& .MuiChartsAxis-tick': {
            stroke: '#e0e0e0',
          },
          '& .MuiChartsAxis-line': {
            stroke: '#e0e0e0',
          },
          '& .MuiChartsAxis-bottom .MuiChartsAxis-tickLabel': {
            // transform: 'rotate(5deg) translateY(-2px)',
            fill: '#666',
            fontSize: '12px',
          },
          '& .MuiBarElement-root': {
            strokeWidth: 2,
          },
          '& .MuiBarElement-series-0': {
            fill: '#1976d220',
            stroke: '#1976d2',
          },
          '& .MuiBarElement-series-1': {
            fill: '#d32f2f20',
            stroke: '#d32f2f',
          },
          '& .MuiBarElement-series-2': {
            fill: '#2e7d3220',
            stroke: '#2e7d32',
          },
          '& .MuiBarElement-series-3': {
            fill: '#ed6c0220',
            stroke: '#ed6c02',
          },
          '& .MuiBarElement-series-4': {
            fill: '#9c27b020',
            stroke: '#9c27b0',
          },
        }}
      />
    </Box>
  );
}

'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  TableSortLabel,
} from '@mui/material';
import { Refresh, Delete, Warning } from '@mui/icons-material';

interface PerformanceMetric {
  operation: string;
  duration: number;
  timestamp: number;
  metadata?: Record<string, unknown>;
}

interface PerformanceStats {
  count: number;
  avgDuration: number;
  minDuration: number;
  maxDuration: number;
  slowOperations: PerformanceMetric[];
  allOperations: PerformanceMetric[];
  operationSummary: { [key: string]: { count: number; avgDuration: number; minDuration: number; maxDuration: number } };
}

interface PerformanceData {
  success: boolean;
  stats: PerformanceStats;
  timestamp: string;
}

export default function PerformanceClient() {
  const [data, setData] = useState<PerformanceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedOperation, setSelectedOperation] = useState<string>('');
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [currentTab, setCurrentTab] = useState(0);
  const [sortBy, setSortBy] = useState<'timestamp' | 'duration' | 'operation'>('timestamp');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const fetchPerformanceData = async (operation?: string) => {
    try {
      setLoading(true);
      const url = operation 
        ? `/api/admin/performance?operation=${encodeURIComponent(operation)}`
        : '/api/admin/performance';
      
      const response = await fetch(url);
      const result = await response.json();
      
      if (result.success) {
        setData(result);
        setError(null);
      } else {
        setError(result.error || 'Failed to fetch performance data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const clearMetrics = async () => {
    try {
      const response = await fetch('/api/admin/performance', {
        method: 'DELETE',
      });
      const result = await response.json();
      
      if (result.success) {
        await fetchPerformanceData(selectedOperation);
      } else {
        setError(result.error || 'Failed to clear metrics');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    }
  };

  useEffect(() => {
    fetchPerformanceData(selectedOperation);
  }, [selectedOperation]);

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        fetchPerformanceData(selectedOperation);
      }, 10000);

      return () => clearInterval(interval);
    }
  }, [autoRefresh, selectedOperation]);

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  const getDurationColor = (duration: number) => {
    if (duration < 100) return 'success';
    if (duration < 500) return 'warning';
    return 'error';
  };

  const handleSort = (column: 'timestamp' | 'duration' | 'operation') => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('desc');
    }
  };

  const sortOperations = (operations: PerformanceMetric[]) => {
    return [...operations].sort((a, b) => {
      let aValue: string | number;
      let bValue: string | number;

      switch (sortBy) {
        case 'timestamp':
          aValue = a.timestamp;
          bValue = b.timestamp;
          break;
        case 'duration':
          aValue = a.duration;
          bValue = b.duration;
          break;
        case 'operation':
          aValue = a.operation;
          bValue = b.operation;
          break;
        default:
          return 0;
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortOrder === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
      } else {
        return sortOrder === 'asc' ? (aValue as number) - (bValue as number) : (bValue as number) - (aValue as number);
      }
    });
  };

  if (loading && !data) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Performance Monitoring
        </Typography>
        <Box display="flex" gap={2}>
          <FormControl size="small" sx={{ minWidth: 200 }}>
            <InputLabel>Filter by Operation</InputLabel>
            <Select
              value={selectedOperation}
              label="Filter by Operation"
              onChange={(e) => setSelectedOperation(e.target.value)}
            >
              <MenuItem value="">All Operations</MenuItem>
              <MenuItem value="DB">Database Operations</MenuItem>
              <MenuItem value="API">API Operations</MenuItem>
              <MenuItem value="get-waves">Get Waves</MenuItem>
              <MenuItem value="get-unique">Get Unique Values</MenuItem>
              <MenuItem value="get-response-stats">Response Stats</MenuItem>
              <MenuItem value="filtered-options">Filtered Options</MenuItem>
              <MenuItem value="wave-history">Wave History</MenuItem>
              <MenuItem value="detailed-analytics">Detailed Analytics</MenuItem>
              <MenuItem value="comments">Comments</MenuItem>
            </Select>
          </FormControl>
          <Button
            variant="outlined"
            onClick={() => setAutoRefresh(!autoRefresh)}
            color={autoRefresh ? 'primary' : 'inherit'}
          >
            Auto Refresh {autoRefresh ? 'ON' : 'OFF'}
          </Button>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={() => fetchPerformanceData(selectedOperation)}
            disabled={loading}
          >
            Refresh
          </Button>
          <Button
            variant="outlined"
            color="error"
            startIcon={<Delete />}
            onClick={clearMetrics}
          >
            Clear Metrics
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {data && (
        <>
          {/* Performance Overview Cards */}
          <Box display="flex" flexWrap="wrap" gap={2} sx={{ mb: 4 }}>
            <Card sx={{ flex: '1 1 200px', minWidth: 200 }}>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Total Operations
                </Typography>
                <Typography variant="h4">
                  {data.stats.count}
                </Typography>
              </CardContent>
            </Card>
            <Card sx={{ flex: '1 1 200px', minWidth: 200 }}>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Average Duration
                </Typography>
                <Typography variant="h4">
                  <Chip 
                    label={formatDuration(data.stats.avgDuration)}
                    color={getDurationColor(data.stats.avgDuration)}
                    size="small"
                  />
                </Typography>
              </CardContent>
            </Card>
            <Card sx={{ flex: '1 1 200px', minWidth: 200 }}>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Fastest Operation
                </Typography>
                <Typography variant="h4">
                  <Chip 
                    label={formatDuration(data.stats.minDuration)}
                    color="success"
                    size="small"
                  />
                </Typography>
              </CardContent>
            </Card>
            <Card sx={{ flex: '1 1 200px', minWidth: 200 }}>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Slowest Operation
                </Typography>
                <Typography variant="h4">
                  <Chip 
                    label={formatDuration(data.stats.maxDuration)}
                    color={getDurationColor(data.stats.maxDuration)}
                    size="small"
                  />
                </Typography>
              </CardContent>
            </Card>
          </Box>

          {/* Tabs for different views */}
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
            <Tabs value={currentTab} onChange={(_, newValue) => setCurrentTab(newValue)}>
              <Tab label="Recent Operations" />
              <Tab label="Operation Summary" />
              <Tab label="Slow Operations" />
            </Tabs>
          </Box>

          {/* Tab Content */}
          {currentTab === 0 && data.stats.allOperations && (
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Recent Operations (Last 50)
                </Typography>
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>
                          <TableSortLabel
                            active={sortBy === 'operation'}
                            direction={sortBy === 'operation' ? sortOrder : 'asc'}
                            onClick={() => handleSort('operation')}
                          >
                            Operation
                          </TableSortLabel>
                        </TableCell>
                        <TableCell>
                          <TableSortLabel
                            active={sortBy === 'duration'}
                            direction={sortBy === 'duration' ? sortOrder : 'asc'}
                            onClick={() => handleSort('duration')}
                          >
                            Duration
                          </TableSortLabel>
                        </TableCell>
                        <TableCell>
                          <TableSortLabel
                            active={sortBy === 'timestamp'}
                            direction={sortBy === 'timestamp' ? sortOrder : 'asc'}
                            onClick={() => handleSort('timestamp')}
                          >
                            Timestamp
                          </TableSortLabel>
                        </TableCell>
                        <TableCell>Metadata</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {sortOperations(data.stats.allOperations).map((op, index) => (
                        <TableRow key={index}>
                          <TableCell>{op.operation}</TableCell>
                          <TableCell>
                            <Chip
                              label={formatDuration(op.duration)}
                              color={getDurationColor(op.duration)}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>{formatTimestamp(op.timestamp)}</TableCell>
                          <TableCell>
                            {op.metadata && (
                              <Typography variant="caption" component="pre" sx={{ fontSize: '0.7rem' }}>
                                {JSON.stringify(op.metadata, null, 2)}
                              </Typography>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          )}

          {currentTab === 1 && data.stats.operationSummary && (
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Operation Summary
                </Typography>
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Operation</TableCell>
                        <TableCell>Count</TableCell>
                        <TableCell>Avg Duration</TableCell>
                        <TableCell>Min Duration</TableCell>
                        <TableCell>Max Duration</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {Object.entries(data.stats.operationSummary)
                        .sort(([,a], [,b]) => b.avgDuration - a.avgDuration)
                        .map(([operation, summary]) => (
                        <TableRow key={operation}>
                          <TableCell>{operation}</TableCell>
                          <TableCell>{summary.count}</TableCell>
                          <TableCell>
                            <Chip
                              label={formatDuration(summary.avgDuration)}
                              color={getDurationColor(summary.avgDuration)}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={formatDuration(summary.minDuration)}
                              color={getDurationColor(summary.minDuration)}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={formatDuration(summary.maxDuration)}
                              color={getDurationColor(summary.maxDuration)}
                              size="small"
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          )}

          {currentTab === 2 && data.stats.slowOperations.length > 0 && (
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Box display="flex" alignItems="center" mb={2}>
                  <Warning color="warning" sx={{ mr: 1 }} />
                  <Typography variant="h6">
                    Slow Operations (&gt;1 second)
                  </Typography>
                </Box>
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Operation</TableCell>
                        <TableCell>Duration</TableCell>
                        <TableCell>Timestamp</TableCell>
                        <TableCell>Metadata</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {data.stats.slowOperations.map((op, index) => (
                        <TableRow key={index}>
                          <TableCell>{op.operation}</TableCell>
                          <TableCell>
                            <Chip
                              label={formatDuration(op.duration)}
                              color={getDurationColor(op.duration)}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>{formatTimestamp(op.timestamp)}</TableCell>
                          <TableCell>
                            {op.metadata && (
                              <Typography variant="caption" component="pre">
                                {JSON.stringify(op.metadata, null, 2)}
                              </Typography>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          )}

          <Typography variant="caption" color="textSecondary">
            Last updated: {new Date(data.timestamp).toLocaleString()}
          </Typography>
        </>
      )}
    </Box>
  );
}

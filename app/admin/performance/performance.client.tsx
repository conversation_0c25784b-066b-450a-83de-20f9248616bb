'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON>po<PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import { Refresh, Delete, Warning } from '@mui/icons-material';

interface PerformanceMetric {
  operation: string;
  duration: number;
  timestamp: number;
  metadata?: Record<string, unknown>;
}

interface PerformanceStats {
  count: number;
  avgDuration: number;
  minDuration: number;
  maxDuration: number;
  slowOperations: PerformanceMetric[];
}

interface PerformanceData {
  success: boolean;
  stats: PerformanceStats;
  timestamp: string;
}

export default function PerformanceClient() {
  const [data, setData] = useState<PerformanceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedOperation, setSelectedOperation] = useState<string>('');
  const [autoRefresh, setAutoRefresh] = useState(false);

  const fetchPerformanceData = async (operation?: string) => {
    try {
      setLoading(true);
      const url = operation 
        ? `/api/admin/performance?operation=${encodeURIComponent(operation)}`
        : '/api/admin/performance';
      
      const response = await fetch(url);
      const result = await response.json();
      
      if (result.success) {
        setData(result);
        setError(null);
      } else {
        setError(result.error || 'Failed to fetch performance data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const clearMetrics = async () => {
    try {
      const response = await fetch('/api/admin/performance', {
        method: 'DELETE',
      });
      const result = await response.json();
      
      if (result.success) {
        await fetchPerformanceData(selectedOperation);
      } else {
        setError(result.error || 'Failed to clear metrics');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    }
  };

  useEffect(() => {
    fetchPerformanceData(selectedOperation);
  }, [selectedOperation]);

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        fetchPerformanceData(selectedOperation);
      }, 10000); // Refresh every 10 seconds

      return () => clearInterval(interval);
    }
  }, [autoRefresh, selectedOperation]);

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  const getDurationColor = (duration: number) => {
    if (duration < 100) return 'success';
    if (duration < 500) return 'warning';
    return 'error';
  };

  if (loading && !data) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Performance Monitoring
        </Typography>
        <Box display="flex" gap={2}>
          <FormControl size="small" sx={{ minWidth: 200 }}>
            <InputLabel>Filter by Operation</InputLabel>
            <Select
              value={selectedOperation}
              label="Filter by Operation"
              onChange={(e) => setSelectedOperation(e.target.value)}
            >
              <MenuItem value="">All Operations</MenuItem>
              <MenuItem value="DB">Database Operations</MenuItem>
              <MenuItem value="API">API Operations</MenuItem>
              <MenuItem value="wave-history">Wave History</MenuItem>
              <MenuItem value="detailed-analytics">Detailed Analytics</MenuItem>
              <MenuItem value="comments">Comments</MenuItem>
            </Select>
          </FormControl>
          <Button
            variant="outlined"
            onClick={() => setAutoRefresh(!autoRefresh)}
            color={autoRefresh ? 'primary' : 'inherit'}
          >
            Auto Refresh {autoRefresh ? 'ON' : 'OFF'}
          </Button>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={() => fetchPerformanceData(selectedOperation)}
            disabled={loading}
          >
            Refresh
          </Button>
          <Button
            variant="outlined"
            color="error"
            startIcon={<Delete />}
            onClick={clearMetrics}
          >
            Clear Metrics
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {data && (
        <>
          {/* Performance Overview Cards */}
          <Box display="flex" flexWrap="wrap" gap={2} sx={{ mb: 4 }}>
            <Card sx={{ flex: '1 1 200px', minWidth: 200 }}>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Total Operations
                </Typography>
                <Typography variant="h4">
                  {data.stats.count}
                </Typography>
              </CardContent>
            </Card>
            <Card sx={{ flex: '1 1 200px', minWidth: 200 }}>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Average Duration
                </Typography>
                <Typography variant="h4">
                  <Chip
                    label={formatDuration(data.stats.avgDuration)}
                    color={getDurationColor(data.stats.avgDuration)}
                    size="small"
                  />
                </Typography>
              </CardContent>
            </Card>
            <Card sx={{ flex: '1 1 200px', minWidth: 200 }}>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Fastest Operation
                </Typography>
                <Typography variant="h4">
                  <Chip
                    label={formatDuration(data.stats.minDuration)}
                    color="success"
                    size="small"
                  />
                </Typography>
              </CardContent>
            </Card>
            <Card sx={{ flex: '1 1 200px', minWidth: 200 }}>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Slowest Operation
                </Typography>
                <Typography variant="h4">
                  <Chip
                    label={formatDuration(data.stats.maxDuration)}
                    color={getDurationColor(data.stats.maxDuration)}
                    size="small"
                  />
                </Typography>
              </CardContent>
            </Card>
          </Box>

          {/* Slow Operations Table */}
          {data.stats.slowOperations.length > 0 && (
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Box display="flex" alignItems="center" mb={2}>
                  <Warning color="warning" sx={{ mr: 1 }} />
                  <Typography variant="h6">
                    Slow Operations (&gt;1 second)
                  </Typography>
                </Box>
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Operation</TableCell>
                        <TableCell>Duration</TableCell>
                        <TableCell>Timestamp</TableCell>
                        <TableCell>Metadata</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {data.stats.slowOperations.map((op, index) => (
                        <TableRow key={index}>
                          <TableCell>{op.operation}</TableCell>
                          <TableCell>
                            <Chip 
                              label={formatDuration(op.duration)}
                              color={getDurationColor(op.duration)}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>{formatTimestamp(op.timestamp)}</TableCell>
                          <TableCell>
                            {op.metadata && (
                              <Typography variant="caption" component="pre">
                                {JSON.stringify(op.metadata, null, 2)}
                              </Typography>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          )}

          <Typography variant="caption" color="textSecondary">
            Last updated: {new Date(data.timestamp).toLocaleString()}
          </Typography>
        </>
      )}
    </Box>
  );
}

'use client';

import { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  TextField,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  CircularProgress,
  Alert,
  InputAdornment,
  IconButton,
  Tooltip,
  Pagination,
} from '@mui/material';
import {
  Search as SearchIcon,
  Login as LoginIcon,
  FileCopy as CopyIcon,
  Refresh as RefreshIcon,
  Launch as LaunchIcon,
} from '@mui/icons-material';

interface UserSearchResult {
  email: string;
  userName: string;
  passcode: string;
  waveId: string;
  surveyCount: number;
  debugUrl: string;
  usedAt?: string;
  createdAt: string;
}

interface AdminUserManagerProps {
  onQuickLogin: (email: string, passcode: string) => Promise<void>;
  onCopyToForm: (email: string, passcode: string) => void;
  isLoading: boolean;
}

export default function AdminUserManager({
  onQuickLogin,
  onCopyToForm,
  isLoading,
}: AdminUserManagerProps) {
  const [users, setUsers] = useState<UserSearchResult[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalUsers, setTotalUsers] = useState(0);
  const usersPerPage = 5;

  const fetchUsers = useCallback(async (search: string = '', currentPage: number = 1) => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      if (search.trim()) {
        params.append('search', search.trim());
      }
      params.append('limit', '100'); // Get all users, we'll paginate client-side

      const response = await fetch(`/api/admin/users/search?${params}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch users');
      }

      if (data.success) {
        const allUsers = data.users;
        setTotalUsers(allUsers.length);

        // Client-side pagination
        const startIndex = (currentPage - 1) * usersPerPage;
        const endIndex = startIndex + usersPerPage;
        const paginatedUsers = allUsers.slice(startIndex, endIndex);

        setUsers(paginatedUsers);
      } else {
        throw new Error(data.error || 'Failed to fetch users');
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch users');
      setUsers([]);
      setTotalUsers(0);
    } finally {
      setLoading(false);
    }
  }, [usersPerPage]);

  // Initial load - fetch all users
  useEffect(() => {
    fetchUsers('', page);
  }, [fetchUsers, page]);

  // Search with debounce - reset to page 1 when searching
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setPage(1); // Reset to first page when searching
      fetchUsers(searchTerm, 1);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, fetchUsers]);

  const handleRefresh = () => {
    fetchUsers(searchTerm, page);
  };

  const handlePageChange = (event: React.ChangeEvent<unknown>, newPage: number) => {
    setPage(newPage);
    fetchUsers(searchTerm, newPage);
  };

  const totalPages = Math.ceil(totalUsers / usersPerPage);



  return (
    <Paper sx={{ p: 4 }}>
      <Box sx={{ textAlign: 'center', mb: 3 }}>
        <Typography variant="h5" fontWeight={600} gutterBottom>
          SuperAdmin User Management
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Search and manage user accounts for quick login and testing
        </Typography>
      </Box>

      {/* Search and Controls */}
      <Box sx={{ mb: 3, display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
        <TextField
          fullWidth
          placeholder="Search by name or email..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ flex: 1, minWidth: 300 }}
        />

        <Tooltip title="Refresh data">
          <IconButton onClick={handleRefresh} disabled={loading}>
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Results Table */}
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Email</TableCell>
              <TableCell>Passcode</TableCell>
              <TableCell>Wave ID</TableCell>
              <TableCell align="center">Surveys</TableCell>
              <TableCell align="center">Status</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading && users.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} align="center" sx={{ py: 4 }}>
                  <CircularProgress />
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                    Loading users...
                  </Typography>
                </TableCell>
              </TableRow>
            ) : users.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} align="center" sx={{ py: 4 }}>
                  <Typography variant="body2" color="text.secondary">
                    {searchTerm ? `No users found matching "${searchTerm}"` : 'No users available'}
                  </Typography>
                  {!searchTerm && (
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      This could mean there are no user passcodes in the database yet.
                    </Typography>
                  )}
                </TableCell>
              </TableRow>
            ) : (
              users.map((user, index) => (
                <TableRow key={index} hover>
                  <TableCell>
                    <Typography variant="body2" fontWeight={500}>
                      {user.userName}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" fontFamily="monospace" fontSize="0.85rem">
                      {user.email}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={user.passcode}
                      variant="outlined"
                      sx={{ fontFamily: 'monospace', fontSize: '0.9rem' }}
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" fontFamily="monospace" fontSize="0.8rem">
                      {user.waveId.slice(-8)}...
                    </Typography>
                  </TableCell>
                  <TableCell align="center">
                    <Chip
                      label={user.surveyCount}
                      variant="outlined"
                      color={user.surveyCount > 0 ? 'success' : 'error'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell align="center">
                    <Chip
                      label={user.usedAt ? 'Used' : 'Unused'}
                      variant="outlined"
                      color={user.usedAt ? 'default' : 'primary'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell align="center">
                    <Box sx={{ display: 'flex', gap: 0.5, justifyContent: 'center' }}>
                      <Tooltip title="Quick Login">
                        <IconButton
                          size="small"
                          onClick={() => onQuickLogin(user.email, user.passcode)}
                          disabled={isLoading || user.surveyCount === 0}
                          color="primary"
                        >
                          <LoginIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      
                      <Tooltip title="Copy to Form">
                        <IconButton
                          size="small"
                          onClick={() => onCopyToForm(user.email, user.passcode)}
                          disabled={isLoading}
                        >
                          <CopyIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      
                      <Tooltip title="Debug Info">
                        <IconButton
                          size="small"
                          component="a"
                          href={user.debugUrl}
                          target="_blank"
                        >
                          <LaunchIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      {totalPages > 1 && (
        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center' }}>
          <Pagination
            count={totalPages}
            page={page}
            onChange={handlePageChange}
            color="primary"
            showFirstButton
            showLastButton
          />
        </Box>
      )}

      {users.length > 0 && (
        <Box sx={{ mt: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            Showing {users.length} of {totalUsers} user{totalUsers !== 1 ? 's' : ''}
            {searchTerm && ` matching "${searchTerm}"`}
            {totalPages > 1 && ` (Page ${page} of ${totalPages})`}
          </Typography>
        </Box>
      )}
    </Paper>
  );
}

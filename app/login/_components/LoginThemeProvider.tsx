'use client';

import React, { createContext, useContext } from 'react';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';

interface LoginThemeContextType {
  darkMode: boolean;
  toggleDarkMode: () => void;
}

const LoginThemeContext = createContext<LoginThemeContextType | undefined>(undefined);

export const useLoginTheme = () => {
  const context = useContext(LoginThemeContext);
  if (!context) {
    throw new Error('useLoginTheme must be used within a LoginThemeProvider');
  }
  return context;
};

interface LoginThemeProviderProps {
  children: React.ReactNode;
}

export default function LoginThemeProvider({
  children,
}: LoginThemeProviderProps) {
  // Dark mode is disabled - always use light mode
  const darkMode = false;

  // No-op function for compatibility
  const toggleDarkMode = () => {
    // Dark mode is disabled
  };

  const theme = createTheme({
    palette: {
      mode: 'light', // Always use light mode
      primary: {
        main: '#E6B800', // Gold from logo
        light: '#F4D03F', // Lighter gold
        dark: '#B8860B', // Darker gold
        contrastText: '#000',
      },
      secondary: {
        main: '#e65100', // Darker orange for better contrast
        light: '#ff8f00', // Medium orange
        dark: '#bf360c', // Very dark orange
        contrastText: '#fff',
      },
    },
    typography: {
      fontFamily:
        '"Encode Sans Condensed", "Roboto", "Helvetica", "Arial", sans-serif',
    },
    components: {
      // Ensure the body background is always light
      MuiCssBaseline: {
        styleOverrides: {
          body: {
            backgroundColor: '#ffffff',
            transition: 'background-color 0.3s ease',
          },
        },
      },
    },
  });

  return (
    <LoginThemeContext.Provider value={{ darkMode, toggleDarkMode }}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        {/* Dark mode toggle removed - always light mode */}
        {children}
      </ThemeProvider>
    </LoginThemeContext.Provider>
  );
}

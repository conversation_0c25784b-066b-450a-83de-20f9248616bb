'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import {
	Box,
	Typography,
	TextField,
	Button,
	Alert,
	CircularProgress,
	Paper,
	Container,
} from '@mui/material';
import AdminUserManager from './_components/AdminUserManager';



interface LoginResponse {
	success: boolean;
	user: {
		userName: string;
		userEmail: string;
		waveId: string;
	};
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	surveys: any[];
	passcode: string;
	error?: string;
}

export default function LoginClient() {
	const router = useRouter();
	const [email, setEmail] = useState('');
	const [passcode, setPasscode] = useState('');
	const [isLoading, setIsLoading] = useState(false);
	const [isAutoLoggingIn, setIsAutoLoggingIn] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [isSuperAdmin, setIsSuperAdmin] = useState(false);

	// Check for admin session on component mount
	useEffect(() => {
		const checkAdminSession = async () => {
			try {
				const response = await fetch('/api/admin/auth/session');
				const data = await response.json();

				if (data.success && data.admin && data.admin.role === 'superadmin') {
					setIsSuperAdmin(true);
				}
			} catch (error) {
				console.error('Error checking admin session:', error);
			}
		};

		checkAdminSession();
	}, []);



	const performLogin = useCallback(
		async (emailValue: string, passcodeValue: string) => {
			setIsLoading(true);
			setError(null);

			try {
				const response = await fetch('/api/auth/login', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						email: emailValue,
						passcode: passcodeValue,
					}),
				});

				const data: LoginResponse = await response.json();

				if (!response.ok) {
					throw new Error(data.error || 'Login failed');
				}

				// Store auth info in session storage
				sessionStorage.setItem(
					'authUser',
					JSON.stringify({
						userName: data.user.userName,
						userEmail: data.user.userEmail,
						waveId: data.user.waveId,
						passcode: data.passcode,
						surveys: data.surveys,
					})
				);

				// Redirect to survey dashboard
				router.push('/dashboard');
			} catch (error) {
				console.error('Login error:', error);
				setError(error instanceof Error ? error.message : 'Login failed');
			} finally {
				setIsLoading(false);
				setIsAutoLoggingIn(false);
			}
		},
		[router]
	);

	// Pre-fill form from query params and auto-login if both are provided
	useEffect(() => {
		if (typeof window !== 'undefined') {
			const params = new URLSearchParams(window.location.search);
			const emailParam = params.get('email') || '';
			const passcodeParam = params.get('passcode') || '';

			setEmail(emailParam);
			setPasscode(passcodeParam);

			// Auto-login if both email and passcode are provided
			if (emailParam && passcodeParam) {
				setIsAutoLoggingIn(true);
				performLogin(emailParam, passcodeParam);
			}
		}
	}, [performLogin]);

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!email.trim()) {
			setError('Please enter your email address');
			return;
		}

		if (!passcode.trim()) {
			setError('Please enter your passcode');
			return;
		}

		await performLogin(email.trim(), passcode.trim());
	};



	return (
		<Container maxWidth="md">
			<Box
				sx={{
					minHeight: '100vh',
					display: 'flex',
					alignItems: 'center',
					justifyContent: 'center',
					py: 4,
				}}
			>
				<Box sx={{ width: '100%' }}>
					{/* Auto-login loading state */}
					{isAutoLoggingIn && (
						<Paper
							sx={{ p: 4, maxWidth: 400, mx: 'auto', textAlign: 'center' }}
						>
							<CircularProgress size={48} sx={{ mb: 3 }} />
							<Typography variant="h5" gutterBottom>
								Signing you in...
							</Typography>
							<Typography variant="body2" color="text.secondary">
								Please wait while we authenticate your credentials
							</Typography>
						</Paper>
					)}

					{/* Main Login Form - only show if not auto-logging in */}
					{!isAutoLoggingIn && (
						<Paper sx={{ p: 4, mb: 4, maxWidth: 400, mx: 'auto' }}>
							<Box sx={{ textAlign: 'center', mb: 4 }}>
								<Box
									component="img"
									src="/images/logos/ABI-logo-long.png"
									alt="ABI Logo"
									sx={{
										height: '100%',
										width: '100%',
										mb: 3,
										objectFit: 'contain',
									}}
								/>
								<Typography variant="h4" fontWeight={700} gutterBottom>
									Assessment Survey
								</Typography>
								<Typography variant="body2" color="text.secondary">
									Enter your email address and 6-digit passcode to access your
									surveys
								</Typography>
							</Box>

							<form onSubmit={handleSubmit}>
								<TextField
									fullWidth
									label="Email Address"
									type="email"
									variant="outlined"
									value={email}
									onChange={(e) => setEmail(e.target.value)}
									placeholder="Enter your email address"
									sx={{ mb: 2 }}
									disabled={isLoading}
								/>

								<TextField
									fullWidth
									label="Passcode"
									variant="outlined"
									value={passcode}
									onChange={(e) => setPasscode(e.target.value)}
									placeholder="Enter 6-digit passcode"
									inputProps={{
										maxLength: 6,
										style: {
											textAlign: 'center',
											fontSize: '1.5rem',
											letterSpacing: '0.5rem',
										},
									}}
									sx={{ mb: 3 }}
									disabled={isLoading}
								/>

								{error && (
									<Alert severity="error" sx={{ mb: 3 }}>
										{error}
									</Alert>
								)}

								<Button
									type="submit"
									fullWidth
									variant="contained"
									size="large"
									disabled={isLoading || !email.trim() || !passcode.trim()}
									startIcon={isLoading ? <CircularProgress size={20} /> : null}
									sx={{ mb: 2 }}
								>
									{isLoading ? 'Signing In...' : 'Sign In'}
								</Button>
							</form>

							<Box sx={{ textAlign: 'center', mt: 3 }}>
								<Typography variant="body2" color="text.secondary">
									Don&apos;t have a passcode? Check your email for survey
									invitation.
								</Typography>
								<Typography
									variant="body2"
									color="text.secondary"
									sx={{ mt: 1 }}
								>
									Use the email address where you received the passcode.
								</Typography>
							</Box>
						</Paper>
					)}

					{/* Admin User Management Section - Only for SuperAdmins */}
					{isSuperAdmin && (
						<AdminUserManager
							onQuickLogin={async (email: string, passcode: string) => {
								await performLogin(email, passcode);
							}}
							onCopyToForm={(email: string, passcode: string) => {
								setEmail(email);
								setPasscode(passcode);
								setError(null);
							}}
							isLoading={isLoading}
						/>
					)}


				</Box>
			</Box>
		</Container>
	);
}

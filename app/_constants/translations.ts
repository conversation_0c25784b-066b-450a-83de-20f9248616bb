import { Language } from '../_components/translation-provider';

export interface Translation {
  [key: string]: string;
}

export interface Translations {
  en: Translation;
  es: Translation;
  pt: Translation;
}

export const translations: Translations = {
  en: {
    // Dashboard
    'dashboard.title': 'Your Agency Assessment Surveys',
    'dashboard.surveysCount':
      'You have {count} assessment{plural} to complete.',
    'dashboard.noSurveys': 'No surveys available at this time.',
    'dashboard.startSurvey': 'Start Survey',
    'dashboard.continueSurvey': 'Continue Survey',
    'dashboard.completed': 'Completed',
    'dashboard.yourPasscode': 'Your Passcode:',
    'dashboard.email': 'Email:',
    'dashboard.language': 'Language',
    'dashboard.logout': 'Logout',
    'dashboard.welcome': 'Welcome,',
    'dashboard.pending': 'Pending',
    'dashboard.inProgress': 'In Progress',
    'dashboard.assessment': 'Assessment',
    'dashboard.agencyAssessmentOfABI': 'Assessment of ABInBev',
    'dashboard.abiAssessmentOfAgency': 'ABInBev Assessment of Agency',

    // Survey Questions
    'question.1.name': 'People and Partnership',
    'question.1.text':
      "Agency allocates talented teams according to the client's requirements, fosters partnership with other agencies, makes client aware of possible issues and treats stakeholders with responsibility and respect.",
    'question.2.name': 'Account Management and Process',
    'question.2.text':
      'Agency ensures projects are delivered on time, on brief and on budget, including updated timelines, cost transparency and active budget optimization',
    'question.3.name': 'Strategy',
    'question.3.text':
      'Agency understands our business, industry and customers and provides strategies that work for our brands; best practices from other categories are regularly shared and lessons learned from the past are translated to future strategic thinking',
    'question.4.name': 'Creativity',
    'question.4.text':
      'Agency delivers ideas on-brief and executable, effectively translating ideas into in-market results; agency elevates the level of creativity with bold creative solutions and proactively brings new opportunities to our team.',
    'question.5.name': 'Media Planning and Execution',
    'question.5.text':
      'Agency understands media performance metrics and uses analytics to drive results; agency demonstrates digital focus on implementation beyond traditional advertising and considers main consumer trends; incorporates a test-and-learn mindset, moving with agility to optimize executions with real-time in-market learning.',
    'question.6.name': 'Net Promoter Score',
    'question.6.text':
      'How likely are you to recommend this agency to a colleague?',

    // Survey Interface
    'survey.question': 'Question',
    'survey.next': 'Next',
    'survey.previous': 'Previous',
    'survey.submit': 'Submit',
    'survey.save': 'Save Progress',
    'survey.comment': 'Comment',
    'survey.rating': 'Rating',
    'survey.progress': 'Progress',
    'survey.loading': 'Loading survey...',
    'survey.submitSurvey': 'Submit Survey',
    'survey.saveAndContinue': 'Next Question',
    'survey.alreadyAnswered': 'Already Answered',
    'survey.submitting': 'Submitting...',
    'survey.questionXofY': 'Question {current} of {total}',
    'survey.disagreeCompletely': 'Disagree Completely',
    'survey.agreeCompletely': 'Agree Completely',
    'survey.feedback':
      'Please tell us what is working, and identify any opportunities for improvements:',
    'survey.feedbackPrompt':
      'Please provide feedback outlining your experience with this agency on the above topic. Your feedback should be specific and actionable.',
    'survey.backToDashboard': 'Back to Dashboard',
    'survey.completionMessage':
      'Thank you! Your survey for {agency} has been submitted successfully.',
    'survey.notApplicable': 'Not Applicable',

    // Languages
    'language.english': 'English',
    'language.spanish': 'Español',
    'language.portuguese': 'Português',
  },
  es: {
    // Dashboard
    'dashboard.title': 'Tus encuestas de evaluación de agencia',
    'dashboard.surveysCount':
      'Tienes {count} evaluación{plural} por completar.',
    'dashboard.noSurveys': 'No hay encuestas disponibles en este momento.',
    'dashboard.startSurvey': 'Comenzar encuesta',
    'dashboard.continueSurvey': 'Continuar encuesta',
    'dashboard.completed': 'Completado',
    'dashboard.yourPasscode': 'Tu código de acceso:',
    'dashboard.email': 'Correo electrónico:',
    'dashboard.language': 'Idioma',
    'dashboard.logout': 'Cerrar sesión',
    'dashboard.welcome': 'Bienvenido,',
    'dashboard.pending': 'Pendiente',
    'dashboard.inProgress': 'En progreso',
    'dashboard.assessment': 'Evaluación',
    'dashboard.agencyAssessmentOfABI': 'Evaluación de ABInBev',
    'dashboard.abiAssessmentOfAgency': 'Evaluación de agencia por ABInBev',

    // Survey Questions
    'question.1.name': 'Personas y colaboración',
    'question.1.text':
      'La agencia asigna equipos talentosos según los requisitos del cliente, fomenta la colaboración con otras agencias, informa al cliente sobre posibles problemas y trata a las partes interesadas con responsabilidad y respeto.',
    'question.2.name': 'Gestión de cuentas y procesos',
    'question.2.text':
      'La agencia asegura que los proyectos se entreguen a tiempo, según el briefing y el presupuesto, incluyendo cronogramas actualizados, transparencia de costos y optimización activa del presupuesto.',
    'question.3.name': 'Estrategia',
    'question.3.text':
      'La agencia entiende nuestro negocio, industria y clientes, y proporciona estrategias que funcionan para nuestras marcas; se comparten buenas prácticas de otras categorías y las lecciones aprendidas se aplican al pensamiento estratégico futuro.',
    'question.4.name': 'Creatividad',
    'question.4.text':
      'La agencia entrega ideas ejecutables y alineadas al briefing, traduce eficazmente las ideas en resultados de mercado; eleva el nivel de creatividad con soluciones audaces y proactivamente presenta nuevas oportunidades.',
    'question.5.name': 'Planificación y ejecución de medios',
    'question.5.text':
      'La agencia entiende los indicadores de desempeño de medios y usa análisis para obtener resultados; demuestra enfoque digital más allá de la publicidad tradicional y considera tendencias principales del consumidor; incorpora una mentalidad de prueba y aprendizaje, optimizando ejecuciones con aprendizajes en tiempo real.',
    'question.6.name': 'Net Promoter Score',
    'question.6.text':
      '¿Qué probabilidad hay de que recomiendes esta agencia a un colega?',

    // Survey Interface
    'survey.question': 'Pregunta',
    'survey.next': 'Siguiente',
    'survey.previous': 'Anterior',
    'survey.submit': 'Enviar',
    'survey.save': 'Guardar progreso',
    'survey.comment': 'Comentario',
    'survey.rating': 'Calificación',
    'survey.progress': 'Progreso',
    'survey.loading': 'Cargando encuesta...',
    'survey.submitSurvey': 'Enviar encuesta',
    'survey.saveAndContinue': 'Siguiente pregunta',
    'survey.alreadyAnswered': 'Ya respondido',
    'survey.submitting': 'Enviando...',
    'survey.questionXofY': 'Pregunta {current} de {total}',
    'survey.disagreeCompletely': 'Totalmente en desacuerdo',
    'survey.agreeCompletely': 'Totalmente de acuerdo',
    'survey.feedback':
      'Por favor, dinos qué está funcionando e identifica oportunidades de mejora:',
    'survey.feedbackPrompt':
      'Por favor, proporciona comentarios sobre tu experiencia con esta agencia en el tema anterior. Tus comentarios deben ser específicos y accionables.',
    'survey.backToDashboard': 'Volver al panel',
    'survey.completionMessage':
      '¡Gracias! Tu encuesta para {agency} ha sido enviada exitosamente.',
    'survey.notApplicable': 'No aplica',

    // Languages
    'language.english': 'Inglés',
    'language.spanish': 'Español',
    'language.portuguese': 'Portugués',
  },
  pt: {
    // Dashboard
    'dashboard.title': 'Suas pesquisas de avaliação de agência',
    'dashboard.surveysCount':
      'Você tem {count} avaliação{plural} para completar.',
    'dashboard.noSurveys': 'Nenhuma pesquisa disponível no momento.',
    'dashboard.startSurvey': 'Iniciar pesquisa',
    'dashboard.continueSurvey': 'Continuar pesquisa',
    'dashboard.completed': 'Concluído',
    'dashboard.yourPasscode': 'Seu código de acesso:',
    'dashboard.email': 'E-mail:',
    'dashboard.language': 'Idioma',
    'dashboard.logout': 'Sair',
    'dashboard.welcome': 'Bem-vindo,',
    'dashboard.pending': 'Pendente',
    'dashboard.inProgress': 'Em andamento',
    'dashboard.assessment': 'Avaliação',
    'dashboard.agencyAssessmentOfABI': 'Avaliação da ABInBev',
    'dashboard.abiAssessmentOfAgency': 'Avaliação da agência pela ABInBev',

    // Survey Questions
    'question.1.name': 'Pessoas e parceria',
    'question.1.text':
      'A agência aloca equipes talentosas de acordo com as necessidades do cliente, promove parceria com outras agências, informa o cliente sobre possíveis problemas e trata as partes interessadas com responsabilidade e respeito.',
    'question.2.name': 'Gestão de contas e processos',
    'question.2.text':
      'A agência garante que os projetos sejam entregues no prazo, conforme o briefing e o orçamento, incluindo cronogramas atualizados, transparência de custos e otimização ativa do orçamento.',
    'question.3.name': 'Estratégia',
    'question.3.text':
      'A agência entende nosso negócio, setor e clientes, e fornece estratégias que funcionam para nossas marcas; melhores práticas de outras categorias são compartilhadas e lições aprendidas são aplicadas ao pensamento estratégico futuro.',
    'question.4.name': 'Criatividade',
    'question.4.text':
      'A agência entrega ideias alinhadas ao briefing e executáveis, traduz eficazmente ideias em resultados de mercado; eleva o nível de criatividade com soluções ousadas e traz novas oportunidades proativamente.',
    'question.5.name': 'Planejamento e execução de mídia',
    'question.5.text':
      'A agência entende os indicadores de desempenho de mídia e usa análises para impulsionar resultados; demonstra foco digital além da publicidade tradicional e considera as principais tendências do consumidor; incorpora uma mentalidade de teste e aprendizado, otimizando execuções com aprendizados em tempo real.',
    'question.6.name': 'Net Promoter Score',
    'question.6.text':
      'Qual a probabilidade de você recomendar esta agência a um colega?',

    // Survey Interface
    'survey.question': 'Pergunta',
    'survey.next': 'Próxima',
    'survey.previous': 'Anterior',
    'survey.submit': 'Enviar',
    'survey.save': 'Salvar progresso',
    'survey.comment': 'Comentário',
    'survey.rating': 'Avaliação',
    'survey.progress': 'Progresso',
    'survey.loading': 'Carregando pesquisa...',
    'survey.submitSurvey': 'Enviar pesquisa',
    'survey.saveAndContinue': 'Próxima pergunta',
    'survey.alreadyAnswered': 'Já respondido',
    'survey.submitting': 'Enviando...',
    'survey.questionXofY': 'Pergunta {current} de {total}',
    'survey.disagreeCompletely': 'Discordo totalmente',
    'survey.agreeCompletely': 'Concordo totalmente',
    'survey.feedback':
      'Por favor, diga-nos o que está funcionando e identifique oportunidades de melhoria:',
    'survey.feedbackPrompt':
      'Por favor, forneça feedback sobre sua experiência com esta agência no tópico acima. Seu feedback deve ser específico e acionável.',
    'survey.backToDashboard': 'Voltar ao painel',
    'survey.completionMessage':
      'Obrigado! Sua pesquisa para {agency} foi enviada com sucesso.',
    'survey.notApplicable': 'Não se aplica',

    // Languages
    'language.english': 'Inglês',
    'language.spanish': 'Espanhol',
    'language.portuguese': 'Português',
  },
};

// Helper function to get translation with interpolation support
export function getTranslation(
  language: Language,
  key: string,
  params?: Record<string, string | number>
): string {
  let translation = translations[language][key] || translations.en[key] || key;

  if (params) {
    Object.entries(params).forEach(([paramKey, value]) => {
      translation = translation.replace(`{${paramKey}}`, String(value));
    });
  }

  return translation;
}

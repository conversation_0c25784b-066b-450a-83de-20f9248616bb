'use client';

import React from 'react';
import {
	Box,
	Container,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>,
	CardContent,
	App<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	Link as MuiLink,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Link from 'next/link';

// Styled components for custom styling
const StyledBox = styled(Box)(() => ({
	'&.hero-section': {
		position: 'relative',
		overflow: 'hidden',
		background: 'linear-gradient(to bottom, #0f172a, #020617, #020617)',
		'&::before': {
			content: '""',
			position: 'absolute',
			top: 0,
			left: 0,
			right: 0,
			bottom: 0,
			backgroundImage: `
        linear-gradient(to right, rgba(255,255,255,0.04) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(255,255,255,0.04) 1px, transparent 1px)
      `,
			backgroundSize: '24px 24px',
			opacity: 0.4,
			zIndex: -1,
		},
	},

	'&.section-bg': {
		background: 'linear-gradient(to bottom, #020617, #0f172a)',
	},
	'&.section-bg-alt': {
		background: 'linear-gradient(to bottom, #0f172a, #020617)',
	},
}));

const StyledAppBar = styled(AppBar)(() => ({
	backgroundColor: 'rgba(2, 6, 23, 0.7)',
	backdropFilter: 'blur(8px)',
	borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
	boxShadow: 'none',
}));

// Echo brand colors for homepage
const echoBrand = {
	sky: {
		400: '#38bdf8',
		500: '#0ea5e9',
		300: '#7dd3fc',
	},
	slate: {
		950: '#020617',
		900: '#0f172a',
		300: '#cbd5e1',
	}
};

const StyledButton = styled(Button)(() => ({
	borderRadius: '16px',
	textTransform: 'none',
	fontWeight: 600,
	padding: '12px 24px',
	'&.primary': {
		backgroundColor: echoBrand.sky[500],
		color: echoBrand.slate[950],
		'&:hover': {
			backgroundColor: echoBrand.sky[400],
		},
	},
	'&.secondary': {
		border: '1px solid rgba(255, 255, 255, 0.2)',
		color: '#f8fafc',
		'&:hover': {
			backgroundColor: 'rgba(255, 255, 255, 0.05)',
		},
	},
}));

const LogoMark = styled(Box)(() => ({
	display: 'inline-flex',
	height: '32px',
	width: '32px',
	alignItems: 'center',
	justifyContent: 'center',
	borderRadius: '12px',
	backgroundColor: 'rgba(14, 165, 233, 0.2)', // sky-500 with 20% opacity
	border: '1px solid rgba(56, 189, 248, 0.3)', // sky-400 with 30% opacity
	'& .dot': {
		height: '12px',
		width: '12px',
		borderRadius: '50%',
		backgroundColor: echoBrand.sky[400],
	},
}));

const WaveSVG = styled('svg')(() => ({
	position: 'absolute',
	top: '0',
	left: '50%',
	transform: 'translateX(-50%)',
	opacity: 0.4,
	zIndex: 0,
	pointerEvents: 'none',
	'& path': {
		stroke: echoBrand.sky[400],
	},
}));

const StyledCard = styled(Card)(() => ({
	backdropFilter: 'blur(6px)',
	WebkitBackdropFilter: 'blur(6px)',
	background: 'linear-gradient(180deg, rgba(255,255,255,0.08), rgba(255,255,255,0.04))',
	border: '1px solid rgba(255,255,255,0.12)',
	borderRadius: '16px',
	boxShadow: 'none',
	'& .MuiCardContent-root': {
		backgroundColor: 'transparent',
	},
}));

export default function Homepage() {
	const currentYear = new Date().getFullYear();

	return (
		<Box sx={{ backgroundColor: '#020617', color: '#f8fafc', minHeight: '100vh' }}>
			{/* Header */}
			<StyledAppBar position="sticky" elevation={0}>
				<Toolbar sx={{ maxWidth: '1200px', mx: 'auto', width: '100%', px: 2 }}>
					<Link href="/" style={{ textDecoration: 'none', color: 'inherit' }}>
						<Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
							<LogoMark>
								<Box className="dot" />
							</LogoMark>
							<Typography variant="h6" sx={{ color: '#cbd5e1', fontWeight: 600, letterSpacing: '-0.025em' }}>
								ECHO360
							</Typography>
						</Box>
					</Link>
					<Box sx={{ flexGrow: 1 }} />
					<Box sx={{ display: { xs: 'none', md: 'flex' }, alignItems: 'center', gap: 3 }}>
						<MuiLink href="#platform" sx={{ color: '#cbd5e1', textDecoration: 'none', '&:hover': { color: '#ffffff' } }}>
							Platform
						</MuiLink>
						<MuiLink href="#customers" sx={{ color: '#cbd5e1', textDecoration: 'none', '&:hover': { color: '#ffffff' } }}>
							Who We Serve
						</MuiLink>
						<MuiLink href="#why" sx={{ color: '#cbd5e1', textDecoration: 'none', '&:hover': { color: '#ffffff' } }}>
							Why ECHO360
						</MuiLink>
						<MuiLink href="#contact" sx={{ color: '#cbd5e1', textDecoration: 'none', '&:hover': { color: '#ffffff' } }}>
							Contact
						</MuiLink>
						<MuiLink href="mailto:<EMAIL>" sx={{ textDecoration: 'none' }}>
							<StyledButton className="primary" size="small">
								Request a Demo
							</StyledButton>
						</MuiLink>
					</Box>
				</Toolbar>
			</StyledAppBar>

			{/* Hero Section */}
			<StyledBox className="hero-section">
				<WaveSVG width="1200" height="300" viewBox="0 0 1200 300" fill="none" aria-hidden="true">
					<path d="M0 150c120-60 240-60 360 0s240 60 360 0 240-60 360 0 240 60 360 0" strokeWidth="2" opacity="0.6" />
					<path d="M0 180c120-60 240-60 360 0s240 60 360 0 240-60 360 0 240 60 360 0" strokeWidth="2" opacity="0.35" />
					<path d="M0 210c120-60 240-60 360 0s240 60 360 0 240-60 360 0 240 60 360 0" strokeWidth="2" opacity="0.2" />
				</WaveSVG>
				<Container maxWidth="lg" sx={{ py: { xs: 12, md: 16 }, textAlign: 'center' }}>
					<Typography
						variant="overline"
						sx={{
							color: 'rgba(125, 211, 252, 0.8)', // sky-300 with 80% opacity
							letterSpacing: '0.2em',
							mb: 2,
							display: 'block'
						}}
					>
						Modern 360° Feedback • Seamless • Scalable • White-Label
					</Typography>
					<Typography
						variant="h1"
						sx={{
							fontSize: { xs: '2.5rem', md: '3.75rem' },
							fontWeight: 800,
							letterSpacing: '-0.025em',
							lineHeight: 1.1,
							mb: 3
						}}
					>
						Modern 360° Feedback.{' '}
						<Box component="span" sx={{ color: echoBrand.sky[400] }}>Seamless.</Box>{' '}
						<Box component="span" sx={{ color: echoBrand.sky[400] }}>Scalable.</Box>{' '}
						<Box component="span" sx={{ color: echoBrand.sky[400] }}>White-Label.</Box>
					</Typography>
					<Typography
						variant="h6"
						sx={{
							maxWidth: '768px',
							mx: 'auto',
							color: '#cbd5e1',
							mb: 5
						}}
					>
						ECHO360 provides organizations with branded, high-performance tools for administering short-form 360° assessments — built for clarity, speed, and impact.
					</Typography>
					<Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2, justifyContent: 'center' }}>
						<MuiLink href="mailto:<EMAIL>" sx={{ textDecoration: 'none' }}>
							<StyledButton className="primary" size="large">
								Request a Demo
							</StyledButton>
						</MuiLink>
					</Box>
				</Container>
			</StyledBox>

			{/* Platform Section */}
			<StyledBox className="section-bg" sx={{ py: 10 }}>
				<Container maxWidth="lg">
					<Box sx={{ maxWidth: '768px', mb: 5 }}>
						<Typography variant="overline" sx={{ color: 'rgba(56, 189, 248, 0.8)', letterSpacing: '0.2em' }}>
							Our Platform
						</Typography>
						<Typography variant="h2" sx={{ mt: 1, mb: 2, fontSize: { xs: '2rem', md: '2.5rem' }, fontWeight: 700 }}>
							Purpose-Built for Professional Development
						</Typography>
						<Typography variant="body1" sx={{ color: '#cbd5e1' }}>
							ECHO360 offers a white-label platform designed for HR departments, coaches, and consultants delivering 360° feedback at scale.
						</Typography>
					</Box>

					<Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)', lg: 'repeat(4, 1fr)' }, gap: 3 }}>
						{[
							{
								title: 'Fully Branded Experience',
								description: "Deploy surveys and reports under your organization's name, logo, and domain."
							},
							{
								title: 'Short-Form Surveys for High Engagement',
								description: 'Keep feedback focused and effective with streamlined question sets and faster response rates.'
							},
							{
								title: 'Automated Reporting',
								description: 'Generate visual reports and export-ready insights without the need for manual processing.'
							},
							{
								title: 'Secure & Scalable Infrastructure',
								description: 'Built with enterprise-grade security, our system supports everything from pilot programs to company-wide rollouts.'
							}
						].map((feature, index) => (
							<StyledCard key={index} sx={{ height: '100%' }}>
								<CardContent>
									<Typography variant="h6" sx={{ mb: 1, fontWeight: 600, color: '#f8fafc' }}>
										{feature.title}
									</Typography>
									<Typography variant="body2" sx={{ color: '#cbd5e1' }}>
										{feature.description}
									</Typography>
								</CardContent>
							</StyledCard>
						))}
					</Box>
				</Container>
			</StyledBox>

			{/* Who We Serve Section */}
			<StyledBox className="section-bg-alt" sx={{ py: 10, borderTop: '1px solid rgba(255, 255, 255, 0.05)' }}>
				<Container maxWidth="lg">
					<Box sx={{ maxWidth: '768px', mb: 5 }}>
						<Typography variant="overline" sx={{ color: 'rgba(56, 189, 248, 0.8)', letterSpacing: '0.2em' }}>
							Who We Serve
						</Typography>
						<Typography variant="h2" sx={{ mt: 1, mb: 2, fontSize: { xs: '2rem', md: '2.5rem' }, fontWeight: 700 }}>
							Built for Teams That Build Leaders
						</Typography>
						<Typography variant="body1" sx={{ color: '#cbd5e1' }}>
							ECHO360 supports a range of organizations and professionals focused on growth and performance:
						</Typography>
					</Box>

					<Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)' }, gap: 2 }}>
						{[
							'HR and Talent Development Teams',
							'Executive Coaches and Leadership Consultants',
							'Learning & Development Departments',
							'People & Culture Specialists'
						].map((item, index) => (
							<StyledCard key={index}>
								<CardContent sx={{ py: 2.5 }}>
									<Typography variant="body1" sx={{ color: '#f8fafc' }}>
										{item}
									</Typography>
								</CardContent>
							</StyledCard>
						))}
					</Box>
				</Container>
			</StyledBox>

			{/* Why ECHO360 Section */}
			<StyledBox className="section-bg" sx={{ py: 10 }}>
				<Container maxWidth="lg">
					<Box sx={{ maxWidth: '768px', mb: 5 }}>
						<Typography variant="overline" sx={{ color: 'rgba(56, 189, 248, 0.8)', letterSpacing: '0.2em' }}>
							Why ECHO360
						</Typography>
						<Typography variant="h2" sx={{ mt: 1, fontSize: { xs: '2rem', md: '2.5rem' }, fontWeight: 700 }}>
							A Reliable Partner in Leadership Development
						</Typography>
					</Box>

					<Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: 'repeat(3, 1fr)' }, gap: 3, mb: 5 }}>
						{[
							{
								title: 'Fast Implementation',
								description: 'Get up and running in days, not weeks.'
							},
							{
								title: 'Minimal Overhead',
								description: 'Intuitive workflows with no training required.'
							},
							{
								title: 'Proven Impact',
								description: 'Designed to support high-performing teams and leadership pipelines.'
							}
						].map((item, index) => (
							<StyledCard key={index}>
								<CardContent>
									<Typography variant="h6" sx={{ mb: 1, fontWeight: 600, color: '#f8fafc' }}>
										{item.title}
									</Typography>
									<Typography variant="body2" sx={{ color: '#cbd5e1' }}>
										{item.description}
									</Typography>
								</CardContent>
							</StyledCard>
						))}
					</Box>

					<Typography variant="body1" sx={{ maxWidth: '768px', color: '#cbd5e1' }}>
						Even with a single client, our focus is precision, privacy, and professionalism. We provide the tools — you drive the transformation.
					</Typography>
				</Container>
			</StyledBox>

			{/* Contact Section */}
			<StyledBox className="section-bg-alt" sx={{ py: 10, borderTop: '1px solid rgba(255, 255, 255, 0.05)' }}>
				<Container maxWidth="md" sx={{ textAlign: 'center' }}>
					<Typography variant="h2" sx={{ mb: 2, fontSize: { xs: '2rem', md: '2.5rem' }, fontWeight: 700 }}>
						Let&apos;s Start a Conversation
					</Typography>
					<Typography variant="body1" sx={{ maxWidth: '512px', mx: 'auto', mb: 4, color: '#cbd5e1' }}>
						ECHO360 helps organizations simplify feedback and amplify growth. Get in touch to explore how we can support your people strategy.
					</Typography>
				</Container>
				<Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2, justifyContent: 'center' }}>
					<MuiLink href="mailto:<EMAIL>" sx={{ textDecoration: 'none' }}>
						<StyledButton className="primary" size="large">
							Request a Demo
						</StyledButton>
					</MuiLink>
				</Box>
			</StyledBox>

			{/* Footer */}
			<Box sx={{ borderTop: '1px solid rgba(255, 255, 255, 0.1)' }}>
				<Container maxWidth="lg" sx={{ py: 4 }}>
					<Box sx={{
						display: 'flex',
						flexDirection: { xs: 'column', md: 'row' },
						alignItems: 'center',
						justifyContent: 'space-between',
						gap: 2
					}}>
						<Typography variant="body2" sx={{ color: '#94a3b8' }}>
							© {currentYear} ECHO360. All rights reserved.
						</Typography>
						<Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
							<MuiLink href="#" sx={{ color: '#94a3b8', textDecoration: 'none', '&:hover': { color: '#e2e8f0' } }}>
								Privacy
							</MuiLink>
							<MuiLink href="#" sx={{ color: '#94a3b8', textDecoration: 'none', '&:hover': { color: '#e2e8f0' } }}>
								Terms
							</MuiLink>
							<MuiLink href="#contact" sx={{ color: '#94a3b8', textDecoration: 'none', '&:hover': { color: '#e2e8f0' } }}>
								Contact
							</MuiLink>
						</Box>
					</Box>
				</Container>
			</Box>
		</Box>
	);
}

'use client';

import React from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  App<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Link as MuiLink,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Link from 'next/link';

// Styled components matching the original HTML design
const StyledAppBar = styled(AppBar)(() => ({
  backgroundColor: '#0a1018',
  color: '#fff',
}));

const StyledBox = styled(Box)(() => ({
  '&.hero-section': {
    position: 'relative',
    overflow: 'hidden',
    borderRadius: '18px',
    padding: '6rem 1rem 5rem',
    background: `
      radial-gradient(1200px 400px at 50% -10%, rgba(0,66,107,.16), rgba(0,66,107,0) 60%),
      linear-gradient(180deg, #0b1118 0%, #0a1018 100%)
    `,
    minHeight: 'calc(100vh - 200px)',
    margin: '2rem auto',
    maxWidth: '1240px',
  },
}));



const StyledButton = styled(Button)(() => ({
  margin: '0.25rem',
  textTransform: 'none',
  fontWeight: 600,
  '&.primary': {
    backgroundColor: '#00426b',
    color: '#ffffff',
    borderRadius: '4px',
    padding: '8px 16px',
    '&:hover': {
      backgroundColor: '#006494',
    },
  },
  '&.secondary': {
    backgroundColor: 'rgba(0, 66, 107, 0.22)',
    color: '#c6e6ff',
    borderRadius: '4px',
    padding: '8px 16px',
    border: '1px solid rgba(90, 172, 255, 0.3)',
    '&:hover': {
      backgroundColor: 'rgba(0, 66, 107, 0.3)',
    },
  },
}));

const WavesSvg = () => (
  <Box
    component="svg"
    sx={{
      position: 'absolute',
      left: '50%',
      transform: 'translateX(-50%)',
      top: -32,
      zIndex: -1,
    }}
    width="1200"
    height="300"
    viewBox="0 0 1200 300"
    fill="none"
    aria-hidden="true"
  >
    <path
      d="M0 150c120-60 240-60 360 0s240 60 360 0 240-60 360 0 240 60 360 0"
      stroke="#1f3a56"
      strokeWidth="2"
      opacity="0.16"
    />
    <path
      d="M0 180c120-60 240-60 360 0s240 60 360 0 240-60 360 0 240 60 360 0"
      stroke="#1f3a56"
      strokeWidth="2"
      opacity="0.16"
    />
    <path
      d="M0 210c120-60 240-60 360 0s240 60 360 0 240-60 360 0 240 60 360 0"
      stroke="#1f3a56"
      strokeWidth="2"
      opacity="0.16"
    />
  </Box>
);

export default function NotFound() {
  return (
    <Box sx={{ backgroundColor: '#0b1118', color: '#ffffff', minHeight: '100vh' }}>
      {/* Header */}
      <StyledAppBar position="sticky" elevation={0}>
        <Toolbar sx={{ maxWidth: '1240px', mx: 'auto', width: '100%', px: 2 }}>
          <Link href="/" style={{ textDecoration: 'none', color: 'inherit' }}>
            <Typography variant="h6" sx={{ fontWeight: 700, letterSpacing: '0.02em' }}>
              ECHO360
            </Typography>
          </Link>
          <Box sx={{ flexGrow: 1 }} />
          <Box sx={{ display: { xs: 'none', md: 'flex' }, alignItems: 'center', gap: 3 }}>
            <MuiLink
              href="/"
              sx={{ color: '#5aacff', textDecoration: 'none', margin: '0 0.75rem' }}
            >
              Home
            </MuiLink>
            <MuiLink
              href="/#contact"
              sx={{ color: '#5aacff', textDecoration: 'none', margin: '0 0.75rem' }}
            >
              Contact
            </MuiLink>
          </Box>
          <Box sx={{ display: { xs: 'none', md: 'flex' }, alignItems: 'center', gap: 1, ml: 2 }}>
            <MuiLink href="/#contact" sx={{ textDecoration: 'none' }}>
              <StyledButton className="primary">
                Request a Demo
              </StyledButton>
            </MuiLink>
            <MuiLink href="mailto:<EMAIL>" sx={{ textDecoration: 'none' }}>
              <StyledButton className="secondary">
                Contact Us
              </StyledButton>
            </MuiLink>
          </Box>
        </Toolbar>
      </StyledAppBar>

      {/* Main Content */}
      <Box sx={{ padding: '2rem 1rem', margin: 'auto', maxWidth: '1240px' }}>
        <StyledBox className="hero-section">
          <WavesSvg />
          <Box sx={{ textAlign: 'center', position: 'relative', zIndex: 1 }}>
            <Typography
              variant="overline"
              sx={{
                color: 'rgba(102, 178, 230, 0.9)',
                letterSpacing: '0.18em',
                textTransform: 'uppercase',
                fontSize: '0.8rem',
                display: 'block',
                mb: 1,
              }}
            >
              Error 404
            </Typography>
            <Typography
              variant="h1"
              sx={{
                fontSize: { xs: '2.25rem', md: '4rem' },
                fontWeight: 600,
                lineHeight: 1.05,
                mb: 2,
                '& .accent': {
                  color: '#5aacff',
                },
              }}
            >
              Page Not Found.{' '}
              <Box component="span" className="accent">
                Let&apos;s get you back on track.
              </Box>
            </Typography>
            <Typography
              variant="h6"
              sx={{
                maxWidth: '60ch',
                mx: 'auto',
                mb: 4,
                fontSize: { xs: '1.05rem', md: '1.25rem' },
                color: 'rgba(255, 255, 255, 0.82)',
                fontWeight: 400,
              }}
            >
              The page you&apos;re looking for doesn&apos;t exist or has moved. Choose one of the options below to continue.
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 1, justifyContent: 'center', mt: '1.75rem' }}>
              <Link href="/" style={{ textDecoration: 'none' }}>
                <StyledButton className="primary">
                  Return Home
                </StyledButton>
              </Link>
              <MuiLink href="mailto:<EMAIL>" sx={{ textDecoration: 'none' }}>
                <StyledButton className="secondary">
                  Contact Support
                </StyledButton>
              </MuiLink>
            </Box>
          </Box>
        </StyledBox>
      </Box>

      {/* Footer */}
      <Box sx={{ py: 4, textAlign: 'center', color: 'rgba(255, 255, 255, 0.7)' }}>
        <Container maxWidth="lg">
          <Typography variant="body2">
            © {new Date().getFullYear()} ECHO360. All rights reserved.
          </Typography>
        </Container>
      </Box>
    </Box>
  );
}

# Performance Optimization Guide

## 🚨 Performance Issues Identified

Based on the analysis of your recent commits and codebase, the performance slowdown is likely caused by:

### 1. **Heavy Database Aggregation Queries**
- Multiple complex MongoDB aggregation pipelines with `$lookup` operations
- Unoptimized queries joining responses → surveys → waves collections
- Large data processing without proper pagination

### 2. **Missing Database Indexes**
- Critical indexes missing for new aggregation queries
- Inefficient filtering on commonly queried fields

### 3. **No Caching Strategy**
- Expensive queries being executed repeatedly
- No caching for frequently accessed data

## ✅ Optimizations Applied

### 1. **MongoDB Connection Optimization**
- Added connection pooling with `maxPoolSize: 10`
- Configured timeouts and connection management
- Disabled mongoose buffering for better performance

### 2. **Database Indexes Added**
```javascript
// Response Model
ResponseSchema.index({ waveId: 1 });
ResponseSchema.index({ createdAt: -1 });
ResponseSchema.index({ surveyId: 1, createdAt: -1 });

// Survey Model  
SurveySchema.index({ waveId: 1 });
SurveySchema.index({ agencyName: 1 });
SurveySchema.index({ brand: 1 });
SurveySchema.index({ country: 1 });
SurveySchema.index({ agencyType: 1 });
SurveySchema.index({ assessmentType: 1 });
SurveySchema.index({ isRemoved: 1 });
SurveySchema.index({ waveId: 1, isRemoved: 1 });
```

### 3. **Caching System**
- Added in-memory caching with TTL support
- Applied to expensive wave-history queries (5-minute cache)
- Cache invalidation and cleanup mechanisms

### 4. **Performance Monitoring**
- Added performance monitoring utilities
- Track slow operations (>1 second)
- New admin endpoint: `/api/admin/performance`

### 5. **Next.js Optimizations**
- Enabled gzip compression
- Optimized package imports for MUI
- Improved webpack bundle splitting
- Image optimization settings

### 6. **Query Optimizations**
- Reduced comment query limit from 50 to 25 results
- Added performance monitoring to heavy endpoints

## 🚀 Immediate Actions Required

### 1. **Deploy the Changes**
```bash
# Build and deploy to staging first
npm run build
# Deploy to staging server
# Test performance improvements
# Deploy to production
```

### 2. **Monitor Performance**
After deployment, monitor the new performance endpoint:
```bash
# Check overall performance stats
curl https://your-domain.com/api/admin/performance

# Check specific operation stats
curl https://your-domain.com/api/admin/performance?operation=wave-history
```

### 3. **Database Index Creation**
The new indexes will be created automatically when the models are loaded, but you can verify them in MongoDB:
```javascript
// In MongoDB shell
db.responses.getIndexes()
db.surveys.getIndexes()
```

## 📊 Expected Performance Improvements

### Before Optimizations:
- Heavy aggregation queries: 2-5+ seconds
- No caching: Every request hits database
- Missing indexes: Full collection scans

### After Optimizations:
- Cached queries: ~50ms (cache hits)
- Indexed queries: 100-500ms (cache misses)
- Reduced data transfer: 50% less due to smaller result sets

## 📊 How to Monitor Performance

### 1. **Web Dashboard (Recommended)**
Access the performance monitoring dashboard at:
```
https://your-domain.com/admin/performance
```

**Features:**
- Real-time performance metrics
- Auto-refresh every 10 seconds
- Filter by operation type
- Visual charts and alerts for slow operations
- Clear metrics functionality

### 2. **API Endpoints**

**Get all performance stats:**
```bash
curl https://your-domain.com/api/admin/performance
```

**Filter by specific operation:**
```bash
# Database operations only
curl https://your-domain.com/api/admin/performance?operation=DB

# Specific endpoint
curl https://your-domain.com/api/admin/performance?operation=wave-history
```

**Clear all metrics:**
```bash
curl -X DELETE https://your-domain.com/api/admin/performance
```

### 3. **Command Line Monitoring Script**

Use the included monitoring script for command-line monitoring:

```bash
# Basic monitoring
node scripts/monitor-performance.js --url https://your-domain.com

# Watch mode (refreshes every 10 seconds)
node scripts/monitor-performance.js --url https://your-domain.com --watch

# Filter by operation
node scripts/monitor-performance.js --operation "wave-history" --watch

# Clear metrics
node scripts/monitor-performance.js --clear

# Get raw JSON output
node scripts/monitor-performance.js --json
```

### 4. **What to Monitor**

**Key Metrics:**
- **Average Duration**: Should be <500ms for most operations
- **Slow Operations**: Any operation >1 second needs attention
- **Operation Count**: Track usage patterns
- **Cache Hit Rate**: Monitor through server logs

**Warning Signs:**
- Average duration consistently >1 second
- Multiple slow operations appearing frequently
- Increasing memory usage over time
- High CPU usage during normal operations

### 5. **Monitoring Schedule**

**Daily:**
- Check the performance dashboard
- Look for new slow operations
- Monitor average response times

**Weekly:**
- Review performance trends
- Clear old metrics if needed
- Check for memory leaks

**After Deployments:**
- Monitor for 30 minutes after deployment
- Compare performance before/after
- Check for any new slow operations

## 🔍 Additional Recommendations

### 1. **Short-term (Next Week)**
- Monitor the performance endpoint daily
- Consider adding caching to other heavy endpoints:
  - `/api/responses/detailed-analytics`
  - `/api/responses/comparison`
  - `/api/waves/[waveId]/stats`

### 2. **Medium-term (Next Month)**
- Consider implementing Redis for distributed caching
- Add database query result pagination
- Implement API rate limiting
- Consider database read replicas for analytics queries

### 3. **Long-term (Next Quarter)**
- Consider moving heavy analytics to a separate service
- Implement background job processing for expensive operations
- Add database query optimization monitoring
- Consider implementing GraphQL for more efficient data fetching

## 🛠️ Troubleshooting

### If Performance Issues Persist:

1. **Check the performance monitoring endpoint**:
   ```bash
   curl https://your-domain.com/api/admin/performance
   ```

2. **Look for slow operations in server logs**:
   ```
   ⚠️ Slow operation detected: DB: detailed-analytics-aggregation took 2500ms
   ```

3. **Clear cache if needed**:
   ```bash
   curl -X DELETE https://your-domain.com/api/admin/performance
   ```

4. **Monitor MongoDB performance**:
   - Check for slow queries in MongoDB logs
   - Verify indexes are being used with `explain()`

## 📈 Performance Metrics to Track

- API response times (target: <500ms for cached, <2s for uncached)
- Database query execution times
- Cache hit rates (target: >70%)
- Memory usage on servers
- CPU utilization during peak loads

## 🚨 Warning Signs to Watch For

- API responses taking >3 seconds consistently
- High CPU usage on database server
- Memory leaks (increasing memory usage over time)
- Cache hit rate dropping below 50%
- Frequent "Slow operation detected" warnings in logs

#!/usr/bin/env node

/**
 * Test Performance Monitoring Script
 * 
 * This script makes API calls to trigger performance monitoring
 * so you can see data in the performance dashboard.
 * 
 * Usage:
 *   node scripts/test-performance-monitoring.js [--url <url>]
 */

const https = require('https');
const http = require('http');

// Parse command line arguments
const args = process.argv.slice(2);
let baseUrl = 'http://localhost:3000';

for (let i = 0; i < args.length; i++) {
  if (args[i] === '--url' && args[i + 1]) {
    baseUrl = args[++i];
  }
}

// Make HTTP request
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https');
    const client = isHttps ? https : http;
    
    const request = client.request(url, (response) => {
      let data = '';
      
      response.on('data', (chunk) => {
        data += chunk;
      });
      
      response.on('end', () => {
        resolve({
          status: response.statusCode,
          data: data
        });
      });
    });
    
    request.on('error', (error) => {
      reject(error);
    });
    
    request.end();
  });
}

// Test endpoints that should trigger performance monitoring
const testEndpoints = [
  '/api/waves?excludeSeed=true',
  '/api/surveys/unique-brands',
  '/api/surveys/unique-agencies', 
  '/api/surveys/unique-regions',
  '/api/responses/stats',
  '/api/surveys/filtered-options',
  '/api/responses/detailed-analytics',
  '/api/responses/wave-history?focusType=agency&focusValue=TestAgency',
];

async function testPerformanceMonitoring() {
  console.log('🚀 Testing Performance Monitoring');
  console.log('='.repeat(50));
  console.log(`Base URL: ${baseUrl}`);
  console.log();

  console.log('📊 Making API calls to trigger performance monitoring...');
  
  for (let i = 0; i < testEndpoints.length; i++) {
    const endpoint = testEndpoints[i];
    const url = `${baseUrl}${endpoint}`;
    
    try {
      console.log(`${i + 1}. Testing ${endpoint}...`);
      const start = Date.now();
      const result = await makeRequest(url);
      const duration = Date.now() - start;
      
      if (result.status === 200) {
        console.log(`   ✅ Success (${duration}ms)`);
      } else {
        console.log(`   ⚠️  Status ${result.status} (${duration}ms)`);
      }
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  console.log();
  console.log('🔄 Making multiple calls to generate more data...');
  
  // Make multiple calls to some endpoints to generate more performance data
  const heavyEndpoints = [
    '/api/surveys/filtered-options',
    '/api/responses/detailed-analytics',
    '/api/responses/stats'
  ];
  
  for (let round = 1; round <= 3; round++) {
    console.log(`Round ${round}:`);
    
    for (const endpoint of heavyEndpoints) {
      const url = `${baseUrl}${endpoint}`;
      
      try {
        const start = Date.now();
        await makeRequest(url);
        const duration = Date.now() - start;
        console.log(`   ${endpoint}: ${duration}ms`);
      } catch (error) {
        console.log(`   ${endpoint}: Error - ${error.message}`);
      }
    }
    
    // Delay between rounds
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  console.log();
  console.log('✅ Performance monitoring test completed!');
  console.log();
  console.log('📈 Now check your performance dashboard:');
  console.log(`   ${baseUrl}/admin/performance`);
  console.log();
  console.log('🔍 Or check the API directly:');
  console.log(`   curl ${baseUrl}/api/admin/performance`);
  console.log();
  console.log('💡 You should now see:');
  console.log('   - Total operations > 0');
  console.log('   - Average duration showing actual times');
  console.log('   - Possibly some slow operations if any took >1 second');
}

// Run the test
testPerformanceMonitoring().catch(error => {
  console.error('❌ Test failed:', error);
  process.exit(1);
});

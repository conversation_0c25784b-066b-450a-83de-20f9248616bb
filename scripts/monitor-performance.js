#!/usr/bin/env node

/**
 * Performance Monitoring Script
 * 
 * Usage:
 *   node scripts/monitor-performance.js [options]
 * 
 * Options:
 *   --url <url>        Base URL of your application (default: http://localhost:3000)
 *   --operation <op>   Filter by specific operation
 *   --watch           Watch mode - refresh every 10 seconds
 *   --clear           Clear all metrics
 *   --json            Output raw JSON
 */

const https = require('https');
const http = require('http');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  url: 'http://localhost:3000',
  operation: null,
  watch: false,
  clear: false,
  json: false
};

for (let i = 0; i < args.length; i++) {
  switch (args[i]) {
    case '--url':
      options.url = args[++i];
      break;
    case '--operation':
      options.operation = args[++i];
      break;
    case '--watch':
      options.watch = true;
      break;
    case '--clear':
      options.clear = true;
      break;
    case '--json':
      options.json = true;
      break;
    case '--help':
      console.log(`
Performance Monitoring Script

Usage:
  node scripts/monitor-performance.js [options]

Options:
  --url <url>        Base URL of your application (default: http://localhost:3000)
  --operation <op>   Filter by specific operation (e.g., "wave-history", "DB")
  --watch           Watch mode - refresh every 10 seconds
  --clear           Clear all metrics
  --json            Output raw JSON
  --help            Show this help message

Examples:
  node scripts/monitor-performance.js --url https://your-domain.com
  node scripts/monitor-performance.js --operation "wave-history" --watch
  node scripts/monitor-performance.js --clear
      `);
      process.exit(0);
  }
}

// Make HTTP request
function makeRequest(url, method = 'GET') {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https');
    const client = isHttps ? https : http;
    
    const request = client.request(url, { method }, (response) => {
      let data = '';
      
      response.on('data', (chunk) => {
        data += chunk;
      });
      
      response.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          resolve(parsed);
        } catch (error) {
          reject(new Error(`Failed to parse JSON: ${error.message}`));
        }
      });
    });
    
    request.on('error', (error) => {
      reject(error);
    });
    
    request.end();
  });
}

// Format duration
function formatDuration(ms) {
  if (ms < 1000) return `${ms}ms`;
  return `${(ms / 1000).toFixed(2)}s`;
}

// Format timestamp
function formatTimestamp(timestamp) {
  return new Date(timestamp).toLocaleString();
}

// Display performance stats
function displayStats(data) {
  if (options.json) {
    console.log(JSON.stringify(data, null, 2));
    return;
  }

  console.clear();
  console.log('🚀 Performance Monitor');
  console.log('='.repeat(50));
  console.log(`Last updated: ${new Date(data.timestamp).toLocaleString()}`);
  console.log();

  const stats = data.stats;
  
  console.log('📊 Overview:');
  console.log(`  Total Operations: ${stats.count}`);
  console.log(`  Average Duration: ${formatDuration(stats.avgDuration)}`);
  console.log(`  Fastest: ${formatDuration(stats.minDuration)}`);
  console.log(`  Slowest: ${formatDuration(stats.maxDuration)}`);
  console.log();

  if (stats.slowOperations && stats.slowOperations.length > 0) {
    console.log('⚠️  Slow Operations (>1 second):');
    console.log('-'.repeat(50));
    
    stats.slowOperations.forEach((op, index) => {
      console.log(`${index + 1}. ${op.operation}`);
      console.log(`   Duration: ${formatDuration(op.duration)}`);
      console.log(`   Time: ${formatTimestamp(op.timestamp)}`);
      if (op.metadata) {
        console.log(`   Metadata: ${JSON.stringify(op.metadata)}`);
      }
      console.log();
    });
  } else {
    console.log('✅ No slow operations detected!');
  }
}

// Main function
async function main() {
  try {
    if (options.clear) {
      console.log('Clearing performance metrics...');
      await makeRequest(`${options.url}/api/admin/performance`, 'DELETE');
      console.log('✅ Metrics cleared successfully');
      return;
    }

    const fetchData = async () => {
      let url = `${options.url}/api/admin/performance`;
      if (options.operation) {
        url += `?operation=${encodeURIComponent(options.operation)}`;
      }
      
      const data = await makeRequest(url);
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch performance data');
      }
      
      displayStats(data);
    };

    if (options.watch) {
      console.log('👀 Watch mode enabled - Press Ctrl+C to exit');
      
      // Initial fetch
      await fetchData();
      
      // Set up interval
      setInterval(async () => {
        try {
          await fetchData();
        } catch (error) {
          console.error('❌ Error fetching data:', error.message);
        }
      }, 10000);
      
    } else {
      await fetchData();
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('\n👋 Goodbye!');
  process.exit(0);
});

main();

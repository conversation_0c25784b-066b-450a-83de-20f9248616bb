# Manual Git Prompt Setup for Digital Ocean Server

## Option 1: Use the Automated Script (Recommended)

1. Upload the `setup_git_prompt.sh` script to your server
2. Make it executable: `chmod +x setup_git_prompt.sh`
3. Run it: `./setup_git_prompt.sh`
4. Activate the changes: `source ~/.bashrc`

## Option 2: Manual Setup

If you prefer to set it up manually, follow these steps:

### Step 1: SSH into your Digital Ocean server

```bash
ssh root@your-server-ip
# or if you have a non-root user:
ssh username@your-server-ip
```

### Step 2: Backup your current .bashrc

```bash
cp ~/.bashrc ~/.bashrc.backup.$(date +%Y%m%d_%H%M%S)
```

### Step 3: Edit your .bashrc

```bash
nano ~/.bashrc
```

### Step 4: Add the git prompt functions

Add these functions at the end of your `.bashrc` file:

```bash
# Git-aware prompt setup
# Git branch parsing function
parse_git_branch() {
    git branch 2> /dev/null | sed -e '/^[^*]/d' -e 's/* \(.*\)/(\1)/'
}

# Git status parsing function
parse_git_status() {
    local git_status=""
    local git_dir="$(git rev-parse --git-dir 2>/dev/null)"
    
    if [ -n "$git_dir" ]; then
        # Check if we have uncommitted changes
        if ! git diff --quiet 2>/dev/null; then
            git_status="${git_status}*"
        fi
        
        # Check if we have staged changes
        if ! git diff --cached --quiet 2>/dev/null; then
            git_status="${git_status}+"
        fi
        
        # Check if we have untracked files
        if [ -n "$(git ls-files --others --exclude-standard 2>/dev/null)" ]; then
            git_status="${git_status}?"
        fi
        
        # Check if we're ahead/behind remote
        local ahead_behind="$(git rev-list --count --left-right @{upstream}...HEAD 2>/dev/null)"
        if [ -n "$ahead_behind" ]; then
            local behind="$(echo "$ahead_behind" | cut -f1)"
            local ahead="$(echo "$ahead_behind" | cut -f2)"
            if [ "$ahead" -gt 0 ] && [ "$behind" -gt 0 ]; then
                git_status="${git_status}↕"
            elif [ "$ahead" -gt 0 ]; then
                git_status="${git_status}↑"
            elif [ "$behind" -gt 0 ]; then
                git_status="${git_status}↓"
            fi
        fi
    fi
    
    echo "$git_status"
}

# Enhanced Git-aware prompt with proper color handling
git_prompt() {
    local branch="$(parse_git_branch)"
    local status="$(parse_git_status)"
    
    if [ -n "$branch" ]; then
        if [ -n "$status" ]; then
            # Red for changes
            printf " \001\033[01;31m\002%s%s\001\033[00m\002" "$branch" "$status"
        else
            # Green for clean
            printf " \001\033[01;32m\002%s\001\033[00m\002" "$branch"
        fi
    fi
}

# Set up the prompt
PS1='${debian_chroot:+($debian_chroot)}\[\033[01;32m\]\u@\h\[\033[00m\]:\[\033[01;34m\]\w\[\033[00m\]$(git_prompt)\$ '

# If this is an xterm set the title to user@host:dir
case "$TERM" in
xterm*|rxvt*)
    PS1="\[\e]0;${debian_chroot:+($debian_chroot)}\u@\h: \w\a\]$PS1"
    ;;
*)
    ;;
esac
```

### Step 5: Save and exit

In nano: Press `Ctrl+X`, then `Y`, then `Enter`

### Step 6: Activate the changes

```bash
source ~/.bashrc
```

## What You'll See

After setup, your prompt will look like:
- `user@host:/path/to/repo (main)$` - Clean repository (green)
- `user@host:/path/to/repo (main)*$` - Uncommitted changes (red)
- `user@host:/path/to/repo (main)+$` - Staged changes (red)
- `user@host:/path/to/repo (main)?$` - Untracked files (red)
- `user@host:/path/to/repo (main)↑$` - Ahead of remote (red)
- `user@host:/path/to/repo (main)↓$` - Behind remote (red)

## Troubleshooting

If colors don't work, make sure your terminal supports colors:
```bash
echo $TERM
```

If it shows `dumb` or `unknown`, you might need to set:
```bash
export TERM=xterm-256color
```

## Reverting Changes

If you want to revert to your original prompt:
```bash
cp ~/.bashrc.backup.* ~/.bashrc
source ~/.bashrc
```

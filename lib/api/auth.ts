'use server';

import dbConnect from '../mongodb';
import { UserPasscodeModel } from '../models/user-passcode';
import { getSurveysByUser, ISurveyVm } from './surveys';

export interface IAuthResult {
  success: boolean;
  user?: {
    userName: string;
    userEmail: string;
    waveId: string;
  };
  surveys?: ISurveyVm[];
  passcode?: string;
  error?: string;
}

export async function authenticateUser(
  email: string,
  passcode: string
): Promise<IAuthResult> {
  try {
    await dbConnect();

    // Find passcode record that matches both email and passcode (case-insensitive email match)
    const userPasscode = await UserPasscodeModel.findOne({
      userEmail: { $regex: new RegExp(`^${email.trim().replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}$`, 'i') },
      passcode: passcode.trim(),
      isActive: true
    });

    if (!userPasscode) {
      return {
        success: false,
        error: 'Invalid email and passcode combination'
      };
    }

    // Check if passcode is expired
    if (userPasscode.expiresAt && userPasscode.expiresAt < new Date()) {
      return {
        success: false,
        error: 'Passcode has expired'
      };
    }

    // Mark passcode as used if not already
    if (!userPasscode.usedAt) {
      await UserPasscodeModel.findByIdAndUpdate(userPasscode._id, {
        usedAt: new Date()
      });
    }

    // Get user surveys
    const surveys = await getSurveysByUser(
      userPasscode.userEmail,
      userPasscode.waveId.toString()
    );

    if (surveys.length === 0) {
      return {
        success: false,
        error: 'No surveys found for this user'
      };
    }

    // Return successful authentication
    return {
      success: true,
      user: {
        userName: surveys[0].userName,
        userEmail: surveys[0].userEmail,
        waveId: surveys[0].waveId,
      },
      surveys: surveys,
      passcode: passcode.trim()
    };

  } catch (error) {
    console.error('Authentication error:', error);
    return {
      success: false,
      error: 'Authentication failed'
    };
  }
} 
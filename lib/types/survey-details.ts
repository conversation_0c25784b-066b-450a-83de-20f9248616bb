export interface ISurveyDetail {
  id: string;
  userName: string;
  userEmail: string;
  agencyName: string;
  agencyType: string;
  brand: string;
  country: string;
  region: string;
  assessmentType: string;
  accountName: string;
  userStatus: string;
  inScope: string;
  notes: string;
  passcode?: string;
  quickLoginUrl?: string;
  surveyStatus: 'completed' | 'in_progress' | 'not_started';
  responseCount: number;
  totalQuestions: number;
  completionPercentage: number;
  lastActivity?: string;
  isRemoved?: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ISurveyAction {
  id: string;
  label: string;
  icon?: string;
  variant: 'contained' | 'outlined' | 'text';
  color: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
  disabled?: boolean;
  tooltip?: string;
}

export interface ISurveyDetailWithActions extends ISurveyDetail {
  availableActions: ISurveyAction[];
}

export type SurveyActionType =
  | 'remove'
  | 'restore'
  | 'resend_invite'
  | 'send_reminder'
  | 'edit'
  | 'save'
  | 'cancel';

export interface ISurveyActionRequest {
  surveyId: string;
  action: SurveyActionType;
  data?: {
    // For edit action
    userName?: string;
    userEmail?: string;
    agencyName?: string;
    brand?: string;
    country?: string;
    region?: string;
    notes?: string;
    // For remove action
    reason?: string;
  };
}

export interface ISurveyActionResponse {
  success: boolean;
  message?: string;
  updatedSurvey?: ISurveyDetail;
  error?: string;
}

// Helper function to determine available actions based on survey status
export function getAvailableActions(survey: ISurveyDetail): ISurveyAction[] {
  const actions: ISurveyAction[] = [];

  if (survey.isRemoved) {
    // Removed surveys can only be restored
    actions.push({
      id: 'restore',
      label: 'Restore',
      variant: 'outlined',
      color: 'success',
      tooltip: 'Restore this survey to results'
    });
    return actions;
  }

  if (survey.surveyStatus === 'completed') {
    // Completed surveys can only be removed
    actions.push({
      id: 'remove',
      label: 'Remove',
      variant: 'outlined',
      color: 'error',
      tooltip: 'Remove this survey from results'
    });
  } else {
    // Non-completed surveys have all actions available
    actions.push(
      {
        id: 'edit',
        label: 'Edit',
        variant: 'outlined',
        color: 'primary',
        tooltip: 'Edit survey details'
      },
      {
        id: 'resend_invite',
        label: 'Resend Invite',
        variant: 'outlined',
        color: 'info',
        tooltip: 'Resend invitation email'
      },
      {
        id: 'send_reminder',
        label: 'Send Reminder',
        variant: 'outlined',
        color: 'warning',
        tooltip: 'Send reminder email'
      },
      {
        id: 'remove',
        label: 'Remove',
        variant: 'outlined',
        color: 'error',
        tooltip: 'Remove this survey from results'
      }
    );
  }

  return actions;
}

// Helper function to get status color
export function getSurveyStatusColor(
  status: ISurveyDetail['surveyStatus']
): 'success' | 'warning' | 'error' | 'info' {
  switch (status) {
    case 'completed':
      return 'success';
    case 'in_progress':
      return 'warning';
    case 'not_started':
      return 'error';
    default:
      return 'info';
  }
}

// Helper function to format status display text
export function formatSurveyStatus(status: ISurveyDetail['surveyStatus']): string {
  switch (status) {
    case 'completed':
      return 'Completed';
    case 'in_progress':
      return 'In Progress';
    case 'not_started':
      return 'Not Started';
    default:
      return 'Unknown';
  }
}

// Helper function to format last activity
export function formatLastActivity(lastActivity?: string): string {
  if (!lastActivity) return 'Never';
  
  const date = new Date(lastActivity);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) {
    return 'Today';
  } else if (diffDays === 1) {
    return 'Yesterday';
  } else if (diffDays < 7) {
    return `${diffDays} days ago`;
  } else {
    return date.toLocaleDateString();
  }
}

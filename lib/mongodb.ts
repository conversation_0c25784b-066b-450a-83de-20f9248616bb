import mongoose from 'mongoose';

const MONGODB_URI = process.env.MONGODB_URI as string;

// Optimized connection options for production performance
const options = {
  maxPoolSize: 10, // Maintain up to 10 socket connections
  serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
  socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
  bufferCommands: false, // Disable mongoose buffering
  bufferMaxEntries: 0, // Disable mongoose buffering
  maxIdleTimeMS: 30000, // Close connections after 30 seconds of inactivity
  family: 4 // Use IPv4, skip trying IPv6
};

if (!MONGODB_URI) {
  throw new Error('Please define the MONGODB_URI environment variable.');
}

let isDbConnected: boolean = false;

export default async function dbConnect() {
  if (process.env.NODE_ENV === 'development') {
    const globalWithMongoDb = global as typeof globalThis & {
      isDbConnected: boolean;
    };
    if (globalWithMongoDb.isDbConnected) return;

    await mongoose.connect(MONGODB_URI, options);

    globalWithMongoDb.isDbConnected = true;
  } else {
    if (isDbConnected) return;

    await mongoose.connect(MONGODB_URI!, options);

    isDbConnected = true;
  }

  console.log('✅ MongoDB connected');
}

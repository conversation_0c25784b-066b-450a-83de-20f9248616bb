// Performance monitoring utilities

interface PerformanceMetric {
  operation: string;
  duration: number;
  timestamp: number;
  metadata?: Record<string, unknown>;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private maxMetrics = 1000; // Keep last 1000 metrics

  // Time an async operation
  async timeOperation<T>(
    operation: string,
    fn: () => Promise<T>,
    metadata?: Record<string, unknown>
  ): Promise<T> {
    const start = Date.now();

    try {
      const result = await fn();
      const duration = Date.now() - start;

      this.recordMetric({
        operation,
        duration,
        timestamp: start,
        metadata
      });

      // Log slow operations (> 1 second)
      if (duration > 1000) {
        console.warn(`⚠️ Slow operation detected: ${operation} took ${duration}ms`, metadata);
      }

      return result;
    } catch (error) {
      const duration = Date.now() - start;

      this.recordMetric({
        operation: `${operation} (ERROR)`,
        duration,
        timestamp: start,
        metadata: { ...metadata, error: error instanceof Error ? error.message : 'Unknown error' }
      });


      throw error;
    }
  }

  private recordMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric);
    
    // Keep only the most recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }
  }

  // Get performance statistics
  getStats(operation?: string): {
    count: number;
    avgDuration: number;
    minDuration: number;
    maxDuration: number;
    slowOperations: PerformanceMetric[];
  } {
    const filteredMetrics = operation 
      ? this.metrics.filter(m => m.operation.includes(operation))
      : this.metrics;

    if (filteredMetrics.length === 0) {
      return {
        count: 0,
        avgDuration: 0,
        minDuration: 0,
        maxDuration: 0,
        slowOperations: []
      };
    }

    const durations = filteredMetrics.map(m => m.duration);
    const slowOperations = filteredMetrics
      .filter(m => m.duration > 1000)
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 10); // Top 10 slowest

    return {
      count: filteredMetrics.length,
      avgDuration: Math.round(durations.reduce((a, b) => a + b, 0) / durations.length),
      minDuration: Math.min(...durations),
      maxDuration: Math.max(...durations),
      slowOperations
    };
  }

  // Clear all metrics
  clear(): void {
    this.metrics = [];
  }
}

// Create a global singleton that persists across module reloads in development
declare global {
  var __performanceMonitor: PerformanceMonitor | undefined;
}

// Export singleton instance that persists across hot reloads
export const performanceMonitor = globalThis.__performanceMonitor ?? new PerformanceMonitor();

if (process.env.NODE_ENV === 'development') {
  globalThis.__performanceMonitor = performanceMonitor;
}

// Helper function to wrap database operations with performance monitoring
export async function monitorDbOperation<T>(
  operation: string,
  fn: () => Promise<T>,
  metadata?: Record<string, unknown>
): Promise<T> {
  return performanceMonitor.timeOperation(`DB: ${operation}`, fn, metadata);
}

// Helper function to wrap API operations with performance monitoring
export async function monitorApiOperation<T>(
  operation: string,
  fn: () => Promise<T>,
  metadata?: Record<string, unknown>
): Promise<T> {
  return performanceMonitor.timeOperation(`API: ${operation}`, fn, metadata);
}
